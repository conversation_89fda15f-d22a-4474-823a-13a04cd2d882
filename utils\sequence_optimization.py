#!/usr/bin/env python3
"""
Sequence Optimization Module for SAITS Model

This module provides intelligent sequence length auto-adjustment and adaptive
sequence creation to resolve the critical issue where valid intervals in well
log data are too short for requested sequence lengths.

Root Cause Analysis:
- 8 wells with 72 total rows (average 9 rows per well)
- Enhanced sequence creation detects valid intervals too short for lengths 64/32/16
- Only sequence length 8 successfully creates sequences

Solution:
- Intelligent sequence length determination based on actual data distribution
- Progressive fallback mechanisms with detailed diagnostics
- Enhanced interval analysis with comprehensive logging
- Automatic parameter adjustment for optimal sequence generation
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import warnings
from dataclasses import dataclass


@dataclass
class DataCharacteristics:
    """Data characteristics for intelligent sequence optimization."""
    total_wells: int
    total_rows: int
    avg_rows_per_well: float
    min_rows_per_well: int
    max_rows_per_well: int
    valid_intervals: List[Tuple[str, int, int]]  # (well, start, end)
    max_continuous_length: int
    recommended_sequence_length: int
    quality_score: float


class SequenceOptimizer:
    """Intelligent sequence length optimizer for well log data."""
    
    def __init__(self, min_sequence_length: int = 4, max_sequence_length: int = 128):
        self.min_sequence_length = min_sequence_length
        self.max_sequence_length = max_sequence_length
        self.analysis_cache = {}
        
    def analyze_data_characteristics(self, df: pd.DataFrame, well_col: str, 
                                   feature_cols: List[str]) -> DataCharacteristics:
        """Analyze data characteristics to determine optimal sequence parameters."""
        
        print("\n🔍 Analyzing Data Characteristics for Sequence Optimization...")
        
        wells = df[well_col].unique()
        total_wells = len(wells)
        total_rows = len(df)
        
        well_row_counts = []
        valid_intervals = []
        max_continuous_length = 0
        
        for well in wells:
            well_df = df[df[well_col] == well].copy()
            well_data = well_df[feature_cols].values
            well_row_counts.append(len(well_df))
            
            # Find valid intervals for this well
            all_valid = ~np.isnan(well_data).any(axis=1)
            
            # Find continuous valid intervals
            if len(all_valid) > 0:
                breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]
                
                for i in range(len(breaks) - 1):
                    start_idx = breaks[i]
                    end_idx = breaks[i + 1]
                    
                    if all_valid[start_idx]:  # Valid interval
                        interval_length = end_idx - start_idx
                        valid_intervals.append((well, start_idx, end_idx))
                        max_continuous_length = max(max_continuous_length, interval_length)
        
        avg_rows_per_well = np.mean(well_row_counts) if well_row_counts else 0
        min_rows_per_well = min(well_row_counts) if well_row_counts else 0
        max_rows_per_well = max(well_row_counts) if well_row_counts else 0
        
        # Calculate recommended sequence length
        recommended_length = self._calculate_optimal_sequence_length(
            max_continuous_length, avg_rows_per_well, len(valid_intervals)
        )
        
        # Calculate quality score
        quality_score = self._calculate_data_quality_score(
            total_wells, total_rows, avg_rows_per_well, max_continuous_length, len(valid_intervals)
        )
        
        characteristics = DataCharacteristics(
            total_wells=total_wells,
            total_rows=total_rows,
            avg_rows_per_well=avg_rows_per_well,
            min_rows_per_well=min_rows_per_well,
            max_rows_per_well=max_rows_per_well,
            valid_intervals=valid_intervals,
            max_continuous_length=max_continuous_length,
            recommended_sequence_length=recommended_length,
            quality_score=quality_score
        )
        
        self._print_analysis_summary(characteristics)
        return characteristics
    
    def _calculate_optimal_sequence_length(self, max_continuous: int, 
                                         avg_rows: float, num_intervals: int) -> int:
        """Calculate optimal sequence length based on data characteristics."""
        
        # Conservative approach: use 60% of max continuous length
        # This ensures we can create multiple sequences from each interval
        base_length = int(max_continuous * 0.6)
        
        # Ensure minimum viable length
        base_length = max(base_length, self.min_sequence_length)
        
        # Cap at maximum allowed length
        base_length = min(base_length, self.max_sequence_length)
        
        # Adjust based on data density
        if avg_rows < 10:  # Very sparse data
            base_length = min(base_length, 6)
        elif avg_rows < 20:  # Sparse data
            base_length = min(base_length, 12)
        elif avg_rows < 50:  # Medium density
            base_length = min(base_length, 24)
        
        # Ensure it's a reasonable power of 2 or common sequence length
        common_lengths = [4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128]
        optimal_length = min(common_lengths, key=lambda x: abs(x - base_length))
        
        return max(optimal_length, self.min_sequence_length)
    
    def _calculate_data_quality_score(self, total_wells: int, total_rows: int,
                                    avg_rows: float, max_continuous: int,
                                    num_intervals: int) -> float:
        """Calculate data quality score (0-1, higher is better)."""
        
        # Factors contributing to quality
        well_factor = min(total_wells / 10, 1.0)  # Normalize to 10 wells
        density_factor = min(avg_rows / 50, 1.0)  # Normalize to 50 rows per well
        continuity_factor = min(max_continuous / 64, 1.0)  # Normalize to 64 continuous
        interval_factor = min(num_intervals / total_wells, 1.0)  # At least 1 interval per well
        
        # Weighted average
        quality_score = (
            well_factor * 0.2 +
            density_factor * 0.3 +
            continuity_factor * 0.3 +
            interval_factor * 0.2
        )
        
        return quality_score
    
    def _print_analysis_summary(self, characteristics: DataCharacteristics):
        """Print detailed analysis summary."""
        
        print(f"\n📊 Data Characteristics Analysis:")
        print(f"   • Total wells: {characteristics.total_wells}")
        print(f"   • Total rows: {characteristics.total_rows}")
        print(f"   • Average rows per well: {characteristics.avg_rows_per_well:.1f}")
        print(f"   • Row range per well: {characteristics.min_rows_per_well} - {characteristics.max_rows_per_well}")
        print(f"   • Valid intervals found: {len(characteristics.valid_intervals)}")
        print(f"   • Maximum continuous length: {characteristics.max_continuous_length}")
        print(f"   • Data quality score: {characteristics.quality_score:.3f}")
        
        print(f"\n🎯 Sequence Optimization Results:")
        print(f"   • Recommended sequence length: {characteristics.recommended_sequence_length}")
        
        if characteristics.quality_score < 0.3:
            print(f"   ⚠️ LOW QUALITY DATA - Consider data preprocessing or collection")
        elif characteristics.quality_score < 0.6:
            print(f"   📊 MEDIUM QUALITY DATA - Sequence optimization recommended")
        else:
            print(f"   ✅ HIGH QUALITY DATA - Standard processing suitable")
        
        # Detailed interval analysis
        if len(characteristics.valid_intervals) <= 10:  # Show details for small datasets
            print(f"\n📋 Valid Intervals Detail:")
            for well, start, end in characteristics.valid_intervals:
                length = end - start
                print(f"   • {well}: rows {start}-{end} (length: {length})")
    
    def get_progressive_sequence_lengths(self, characteristics: DataCharacteristics) -> List[int]:
        """Get list of sequence lengths to try in order of preference."""
        
        recommended = characteristics.recommended_sequence_length
        max_possible = characteristics.max_continuous_length
        
        # Start with recommended, then try progressively smaller lengths
        lengths = [recommended]
        
        # Add smaller lengths as fallbacks
        fallback_lengths = [32, 24, 16, 12, 8, 6, 4]
        for length in fallback_lengths:
            if length < recommended and length <= max_possible and length not in lengths:
                lengths.append(length)
        
        # Ensure we have at least the minimum
        if self.min_sequence_length not in lengths:
            lengths.append(self.min_sequence_length)
        
        # Sort by preference (larger first, but not exceeding max_possible)
        lengths = [l for l in lengths if l <= max_possible]
        lengths.sort(reverse=True)
        
        return lengths
    
    def validate_sequence_feasibility(self, characteristics: DataCharacteristics,
                                    sequence_length: int) -> Tuple[bool, str]:
        """Validate if a sequence length is feasible for the given data."""
        
        if sequence_length > characteristics.max_continuous_length:
            return False, f"Sequence length {sequence_length} > max continuous length {characteristics.max_continuous_length}"
        
        if sequence_length < self.min_sequence_length:
            return False, f"Sequence length {sequence_length} < minimum allowed {self.min_sequence_length}"
        
        # Estimate number of sequences that could be created
        estimated_sequences = 0
        for well, start, end in characteristics.valid_intervals:
            interval_length = end - start
            if interval_length >= sequence_length:
                # Assuming stride of 1
                estimated_sequences += interval_length - sequence_length + 1
        
        if estimated_sequences == 0:
            return False, f"No sequences can be created with length {sequence_length}"
        
        if estimated_sequences < 10:  # Minimum viable number of sequences
            return False, f"Only {estimated_sequences} sequences possible (minimum 10 recommended)"
        
        return True, f"Feasible: ~{estimated_sequences} sequences possible"


def create_adaptive_sequence_parameters(df: pd.DataFrame, well_col: str,
                                      feature_cols: List[str],
                                      requested_length: int = 64) -> Dict[str, Any]:
    """Create adaptive sequence parameters based on data analysis."""
    
    optimizer = SequenceOptimizer()
    characteristics = optimizer.analyze_data_characteristics(df, well_col, feature_cols)
    
    # Get progressive sequence lengths to try
    progressive_lengths = optimizer.get_progressive_sequence_lengths(characteristics)
    
    # Validate each length
    feasible_lengths = []
    for length in progressive_lengths:
        is_feasible, reason = optimizer.validate_sequence_feasibility(characteristics, length)
        if is_feasible:
            feasible_lengths.append(length)
            print(f"   ✅ Length {length}: {reason}")
        else:
            print(f"   ❌ Length {length}: {reason}")
    
    if not feasible_lengths:
        print(f"\n⚠️ WARNING: No feasible sequence lengths found!")
        print(f"   Falling back to minimum length: {optimizer.min_sequence_length}")
        feasible_lengths = [optimizer.min_sequence_length]
    
    # Calculate optimal stride
    optimal_stride = max(1, feasible_lengths[0] // 8)  # 12.5% overlap
    
    return {
        'characteristics': characteristics,
        'requested_length': requested_length,
        'recommended_length': characteristics.recommended_sequence_length,
        'feasible_lengths': feasible_lengths,
        'optimal_stride': optimal_stride,
        'quality_score': characteristics.quality_score,
        'optimization_strategy': 'adaptive' if characteristics.quality_score < 0.6 else 'standard'
    }
