#!/usr/bin/env python3
"""
Adaptive Sequence Creator for SAITS Model

This module provides adaptive sequence creation with progressive fallback mechanisms
to resolve the critical issue where traditional sequence creation fails due to
short valid intervals in well log data.

Key Features:
- Progressive fallback with multiple sequence lengths
- Intelligent stride adjustment based on data characteristics
- Comprehensive error handling and recovery
- Detailed logging and diagnostics
- Backward compatibility with existing code
- Automatic parameter optimization
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Union
import warnings
from dataclasses import dataclass
import logging
from datetime import datetime

# Phase 1: Enhanced import handling with comprehensive error recovery
try:
    from ..utils.sequence_optimization import SequenceOptimizer, create_adaptive_sequence_parameters
    SEQUENCE_OPTIMIZATION_AVAILABLE = True
    # print("SequenceOptimizer loaded successfully")
except ImportError as e:
    # print(f"SequenceOptimizer not available: {e}")
    SEQUENCE_OPTIMIZATION_AVAILABLE = False

try:
    from ..utils.interval_analyzer import IntervalAnalyzer, IntervalAnalysisReport
    INTERVAL_ANALYZER_AVAILABLE = True
    # print("IntervalAnalyzer loaded successfully")
except ImportError as e:
    # print(f"IntervalAnalyzer not available: {e}")
    INTERVAL_ANALYZER_AVAILABLE = False

# Phase 1: Fallback implementations for missing dependencies
if not INTERVAL_ANALYZER_AVAILABLE:
    # print("Using fallback IntervalAnalysisReport implementation")
    pass
    
    @dataclass
    class IntervalAnalysisReport:
        """Fallback implementation of IntervalAnalysisReport."""
        total_wells: int = 0
        total_rows: int = 0
        intervals: List = None
        overall_quality: float = 0.0
        recommendations: List[str] = None
        sequence_feasibility: Dict[int, Dict[str, Any]] = None
        analysis_timestamp: str = ""
        
        def __post_init__(self):
            if self.intervals is None:
                self.intervals = []
            if self.recommendations is None:
                self.recommendations = []
            if self.sequence_feasibility is None:
                self.sequence_feasibility = {}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SequenceCreationResult:
    """Result of adaptive sequence creation with comprehensive metadata."""
    sequences: np.ndarray
    metadata: Dict[str, Any]
    success: bool
    method_used: str
    parameters_used: Dict[str, Any]
    fallback_attempts: int
    warnings: List[str]
    performance_metrics: Dict[str, Any]
    
    def __post_init__(self):
        """Ensure all fields are properly initialized."""
        if self.sequences is None:
            self.sequences = np.array([])
        if self.metadata is None:
            self.metadata = {}
        if self.warnings is None:
            self.warnings = []
        if self.performance_metrics is None:
            self.performance_metrics = {}


class AdaptiveSequenceCreator:
    """
    Adaptive sequence creator with progressive fallback mechanisms.
    
    This class implements a robust sequence creation strategy that automatically
    adapts to data characteristics and provides multiple fallback options when
    the primary method fails.
    """
    
    def __init__(self, verbose: bool = True, enable_diagnostics: bool = True):
        """
        Initialize adaptive sequence creator.
        
        Args:
            verbose: Enable detailed logging
            enable_diagnostics: Enable comprehensive diagnostics
        """
        self.verbose = verbose
        self.enable_diagnostics = enable_diagnostics
        self.creation_history = []
        
        # Initialize diagnostic modules if available
        self.sequence_optimizer = None
        self.interval_analyzer = None
        
        if SEQUENCE_OPTIMIZATION_AVAILABLE:
            try:
                from ..utils.sequence_optimization import SequenceOptimizer
                self.sequence_optimizer = SequenceOptimizer()
                if self.verbose:
                    print("✅ SequenceOptimizer initialized")
            except Exception as e:
                if self.verbose:
                    print(f"⚠️ Failed to initialize SequenceOptimizer: {e}")
        
        if INTERVAL_ANALYZER_AVAILABLE:
            try:
                from ..utils.interval_analyzer import IntervalAnalyzer
                self.interval_analyzer = IntervalAnalyzer(verbose=self.verbose)
                if self.verbose:
                    print("✅ IntervalAnalyzer initialized")
            except Exception as e:
                if self.verbose:
                    print(f"⚠️ Failed to initialize IntervalAnalyzer: {e}")
    
    def create_sequences_adaptive(self, df: pd.DataFrame, well_col: str, feature_cols: List[str],
                                sequence_length: int = 64, stride: int = 1,
                                prediction_mode: bool = False, target_well: Optional[str] = None) -> SequenceCreationResult:
        """
        Create sequences with adaptive fallback mechanisms.
        
        Args:
            df: Input dataframe
            well_col: Well identifier column
            feature_cols: Feature columns for sequence creation
            sequence_length: Requested sequence length
            stride: Sequence stride
            prediction_mode: Whether in prediction mode
            target_well: Target well for prediction mode
            
        Returns:
            SequenceCreationResult with sequences and comprehensive metadata
        """
        
        start_time = datetime.now()
        
        if self.verbose:
            print(f"\n🚀 Starting Adaptive Sequence Creation")
            print(f"   • Dataset: {len(df)} rows, {len(df[well_col].unique())} wells")
            print(f"   • Features: {feature_cols}")
            print(f"   • Requested: length={sequence_length}, stride={stride}")
            print(f"   • Mode: {'prediction' if prediction_mode else 'training'}")
        
        # Phase 1: Data analysis and strategy selection
        strategy = self._analyze_data_and_select_strategy(df, well_col, feature_cols, sequence_length)
        
        if self.verbose:
            print(f"   • Strategy: {strategy['name']}")
            print(f"   • Fallback lengths: {strategy['sequence_lengths']}")
        
        # Phase 2: Progressive sequence creation with fallback
        result = self._create_sequences_with_fallback(
            df, well_col, feature_cols, strategy, prediction_mode, target_well
        )
        
        # Phase 3: Performance metrics and finalization
        end_time = datetime.now()
        result.performance_metrics = {
            'creation_time_seconds': (end_time - start_time).total_seconds(),
            'strategy_used': strategy['name'],
            'data_analysis_available': self.sequence_optimizer is not None,
            'interval_analysis_available': self.interval_analyzer is not None
        }
        
        # Store in history
        self.creation_history.append(result)
        
        if self.verbose:
            self._print_creation_summary(result)
        
        return result
    
    def _analyze_data_and_select_strategy(self, df: pd.DataFrame, well_col: str,
                                        feature_cols: List[str], requested_length: int) -> Dict[str, Any]:
        """Analyze data characteristics and select optimal strategy."""
        
        if self.verbose:
            print(f"\n🔍 Analyzing Data Characteristics...")
        
        # Basic data analysis
        wells = df[well_col].unique()
        total_rows = len(df)
        avg_rows_per_well = total_rows / len(wells) if len(wells) > 0 else 0
        
        # Calculate missing rate
        feature_data = df[feature_cols].values
        missing_rate = np.isnan(feature_data).sum() / feature_data.size if feature_data.size > 0 else 1.0
        
        if self.verbose:
            print(f"   • Wells: {len(wells)}")
            print(f"   • Average rows per well: {avg_rows_per_well:.1f}")
            print(f"   • Missing rate: {missing_rate:.1%}")
        
        # Advanced analysis if available
        optimal_length = requested_length
        if self.sequence_optimizer:
            try:
                characteristics = self.sequence_optimizer.analyze_data_characteristics(df, well_col, feature_cols)
                optimal_length = characteristics.recommended_sequence_length
                if self.verbose:
                    print(f"   • Optimizer recommended length: {optimal_length}")
            except Exception as e:
                if self.verbose:
                    print(f"   ⚠️ Optimizer analysis failed: {e}")
        
        # Strategy selection based on data characteristics
        if avg_rows_per_well >= requested_length * 2 and missing_rate < 0.3:
            # High quality data - use standard approach
            return {
                'name': 'standard',
                'sequence_lengths': [requested_length],
                'stride_adjustment': 'none',
                'max_attempts': 1
            }
        
        elif optimal_length >= requested_length * 0.5:
            # Adaptive strategy - use optimal length with fallbacks
            fallback_lengths = [optimal_length]
            for length in [32, 24, 16, 12, 8, 6, 4]:
                if length < optimal_length and length not in fallback_lengths:
                    fallback_lengths.append(length)
                if len(fallback_lengths) >= 3:
                    break
            
            return {
                'name': 'adaptive',
                'sequence_lengths': fallback_lengths,
                'stride_adjustment': 'adaptive',
                'max_attempts': len(fallback_lengths)
            }
        
        else:
            # Aggressive fallback strategy - data is very sparse
            return {
                'name': 'aggressive_fallback',
                'sequence_lengths': [optimal_length, 8, 6, 4],
                'stride_adjustment': 'minimal',
                'max_attempts': 4
            }

    def _create_sequences_with_fallback(self, df: pd.DataFrame, well_col: str,
                                      feature_cols: List[str], strategy: Dict[str, Any],
                                      prediction_mode: bool, target_well: Optional[str]) -> SequenceCreationResult:
        """Create sequences with progressive fallback."""

        last_error = None
        warnings_list = []

        for attempt, seq_length in enumerate(strategy['sequence_lengths']):
            try:
                # Calculate adaptive stride
                stride = self._calculate_adaptive_stride(seq_length, strategy['stride_adjustment'])

                if self.verbose:
                    print(f"\n🔄 Attempt {attempt + 1}/{strategy['max_attempts']}: length={seq_length}, stride={stride}")

                # Try enhanced sequence creation first
                sequences, metadata = self._try_enhanced_creation(
                    df, well_col, feature_cols, seq_length, stride, prediction_mode, target_well
                )

                if sequences.shape[0] > 0:
                    if self.verbose:
                        print(f"   ✅ Success! Created {sequences.shape[0]} sequences")

                    return SequenceCreationResult(
                        sequences=sequences,
                        metadata=metadata,
                        success=True,
                        method_used='enhanced_adaptive',
                        parameters_used={'sequence_length': seq_length, 'stride': stride},
                        fallback_attempts=attempt + 1,
                        warnings=warnings_list,
                        performance_metrics={'strategy': strategy['name']}
                    )

                # Try basic sequence creation as fallback
                sequences, metadata = self._try_basic_creation(
                    df, well_col, feature_cols, seq_length, stride, prediction_mode, target_well
                )

                if sequences.shape[0] > 0:
                    if self.verbose:
                        print(f"   ✅ Success with basic method! Created {sequences.shape[0]} sequences")

                    warnings_list.append(f"Enhanced method failed, used basic method for length {seq_length}")

                    return SequenceCreationResult(
                        sequences=sequences,
                        metadata=metadata,
                        success=True,
                        method_used='basic_adaptive',
                        parameters_used={'sequence_length': seq_length, 'stride': stride},
                        fallback_attempts=attempt + 1,
                        warnings=warnings_list,
                        performance_metrics={'strategy': strategy['name']}
                    )

                if self.verbose:
                    print(f"   ❌ Failed: No sequences created with length {seq_length}")

                warnings_list.append(f"No sequences created with length {seq_length}")

            except Exception as e:
                last_error = e
                error_msg = f"Error with sequence length {seq_length}: {str(e)}"
                warnings_list.append(error_msg)

                if self.verbose:
                    print(f"   ❌ Error: {error_msg}")

                continue

        # All attempts failed
        error_msg = f"All sequence creation attempts failed. Last error: {last_error}"

        return SequenceCreationResult(
            sequences=np.empty((0, strategy['sequence_lengths'][0], len(feature_cols))),
            metadata={'total_attempts': len(strategy['sequence_lengths']), 'strategy': strategy['name']},
            success=False,
            method_used='all_failed',
            parameters_used={},
            fallback_attempts=len(strategy['sequence_lengths']),
            warnings=warnings_list,
            performance_metrics={'last_error': str(last_error) if last_error else 'Unknown'}
        )

    def _calculate_adaptive_stride(self, sequence_length: int, adjustment_type: str) -> int:
        """Calculate adaptive stride based on sequence length and strategy."""

        if adjustment_type == 'none':
            return 1
        elif adjustment_type == 'minimal':
            return max(1, sequence_length // 16)  # Very small stride
        elif adjustment_type == 'adaptive':
            return max(1, sequence_length // 8)   # Moderate stride
        else:
            return 1

    def _try_enhanced_creation(self, df: pd.DataFrame, well_col: str,
                             feature_cols: List[str], sequence_length: int,
                             stride: int, prediction_mode: bool,
                             target_well: Optional[str]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Try enhanced sequence creation method with timeout protection."""

        try:
            if self.verbose:
                print("🔧 Attempting enhanced sequence creation...")

            # Import enhanced preprocessing if available
            from .deep.enhanced_preprocessing import enhanced_create_sequences

            if self.verbose:
                print("✅ Enhanced preprocessing imported successfully")
                print(f"   Parameters: length={sequence_length}, stride={stride}")

            # Call enhanced sequence creation
            sequences = enhanced_create_sequences(
                df, well_col, feature_cols, sequence_length, stride
            )

            if self.verbose:
                print(f"✅ Enhanced sequence creation completed: {sequences.shape if hasattr(sequences, 'shape') else 'unknown shape'}")

            metadata = {
                'method': 'enhanced',
                'sequence_length': sequence_length,
                'stride': stride,
                'prediction_mode': prediction_mode,
                'target_well': target_well
            }

            return sequences, metadata

        except ImportError as e:
            if self.verbose:
                print(f"⚠️ Enhanced preprocessing not available: {e}")
            # Enhanced preprocessing not available
            return np.empty((0, sequence_length, len(feature_cols))), {'error': 'enhanced_not_available'}
        except Exception as e:
            if self.verbose:
                print(f"❌ Enhanced method failed: {e}")
            # Enhanced method failed
            return np.empty((0, sequence_length, len(feature_cols))), {'error': str(e)}

    def _try_basic_creation(self, df: pd.DataFrame, well_col: str,
                          feature_cols: List[str], sequence_length: int,
                          stride: int, prediction_mode: bool,
                          target_well: Optional[str]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Try basic sequence creation method."""

        try:
            sequences = self._create_sequences_basic(
                df, well_col, feature_cols, sequence_length, stride,
                prediction_mode, target_well
            )

            metadata = {
                'method': 'basic',
                'sequence_length': sequence_length,
                'stride': stride,
                'prediction_mode': prediction_mode,
                'target_well': target_well
            }

            return sequences, metadata

        except Exception as e:
            return np.empty((0, sequence_length, len(feature_cols))), {'error': str(e)}

    def _create_sequences_basic(self, df: pd.DataFrame, well_col: str,
                              feature_cols: List[str], sequence_length: int,
                              stride: int, prediction_mode: bool,
                              target_well: Optional[str]) -> np.ndarray:
        """Basic sequence creation implementation."""

        sequences = []
        wells_to_process = [target_well] if target_well else df[well_col].unique()

        for well in wells_to_process:
            well_df = df[df[well_col] == well].copy()
            well_data = well_df[feature_cols].values

            if len(well_data) == 0:
                continue

            # Find valid continuous intervals
            all_valid = ~np.isnan(well_data).any(axis=1)

            if not np.any(all_valid):
                continue

            # Find interval boundaries
            breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]

            for i in range(len(breaks) - 1):
                start_idx = breaks[i]
                end_idx = breaks[i + 1]

                if all_valid[start_idx]:  # Valid interval
                    interval_data = well_data[start_idx:end_idx]

                    # Create sequences from this interval
                    for seq_start in range(0, len(interval_data) - sequence_length + 1, stride):
                        seq_end = seq_start + sequence_length
                        sequence = interval_data[seq_start:seq_end]

                        if sequence.shape[0] == sequence_length:
                            sequences.append(sequence)

        if sequences:
            return np.array(sequences)
        else:
            return np.empty((0, sequence_length, len(feature_cols)))

    def _print_creation_summary(self, result: SequenceCreationResult):
        """Print summary of sequence creation results."""

        if self.verbose:
            print(f"\n📊 Sequence Creation Summary:")
            print(f"   • Success: {'✅ Yes' if result.success else '❌ No'}")
            print(f"   • Method used: {result.method_used}")
            print(f"   • Fallback attempts: {result.fallback_attempts}")

            if result.success:
                print(f"   • Sequences created: {result.sequences.shape[0]}")
                print(f"   • Sequence shape: {result.sequences.shape}")
                print(f"   • Parameters: {result.parameters_used}")

            if result.warnings:
                print(f"   • Warnings ({len(result.warnings)}):")
                for warning in result.warnings:
                    print(f"     ⚠️ {warning}")


# Convenience function for backward compatibility
def create_sequences_adaptive(df: pd.DataFrame, well_col: str, feature_cols: List[str],
                            sequence_length: int = 64, stride: int = 1,
                            prediction_mode: bool = False, target_well: Optional[str] = None,
                            verbose: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """Convenience function for adaptive sequence creation."""

    creator = AdaptiveSequenceCreator(verbose=verbose)
    result = creator.create_sequences_adaptive(
        df, well_col, feature_cols, sequence_length, stride, prediction_mode, target_well
    )

    return result.sequences, result.metadata
