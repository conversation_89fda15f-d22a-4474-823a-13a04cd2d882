#!/usr/bin/env python3
"""
Debug script for enhanced preprocessing tuple unpacking errors in Option 3.

This script helps identify and fix the specific issues causing:
1. Empty sequences (shape (0,)) from enhanced preprocessing
2. <PERSON>ple unpacking errors expecting 3 values but getting 1
3. Enhanced preprocessing chain inconsistencies
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, <PERSON><PERSON>


def debug_enhanced_preprocessing_chain(df: pd.DataFrame, 
                                     feature_cols: List[str], 
                                     target_col: str,
                                     sequence_len: int = 64) -> Dict[str, Any]:
    """
    Debug the enhanced preprocessing chain step by step.
    
    Args:
        df: Input dataframe
        feature_cols: Feature column names
        target_col: Target column name
        sequence_len: Sequence length for deep learning
        
    Returns:
        Dictionary with detailed diagnostic information
    """
    print("🔍 DEBUGGING ENHANCED PREPROCESSING CHAIN")
    print("=" * 60)
    
    all_features = feature_cols + [target_col]
    results = {}
    
    # Step 1: Test enhanced preprocessing availability
    print("📦 Step 1: Testing enhanced preprocessing availability...")
    try:
        from preprocessing.deep.enhanced_preprocessing import (
            enhanced_preprocessing_pipeline,
            enhanced_create_sequences,
            enhanced_introduce_missingness,
            EnhancedLogPreprocessor
        )
        results['enhanced_available'] = True
        print("✅ Enhanced preprocessing modules imported successfully")
    except ImportError as e:
        results['enhanced_available'] = False
        print(f"❌ Enhanced preprocessing import failed: {e}")
        return results
    
    # Step 2: Test enhanced sequence creation
    print(f"\n🔗 Step 2: Testing enhanced sequence creation...")
    print(f"   Dataset: {len(df)} rows, {len(df['WELL'].unique())} wells")
    print(f"   Sequence length: {sequence_len}")
    print(f"   Features: {all_features}")
    
    try:
        # Test the enhanced preprocessor directly
        preprocessor = EnhancedLogPreprocessor(sequence_len=sequence_len, sequence_stride=32)
        sequences, metadata = preprocessor.create_sequences_enhanced(df, 'WELL', all_features)
        
        results['enhanced_sequences'] = {
            'shape': sequences.shape,
            'size': sequences.size,
            'dtype': sequences.dtype,
            'is_valid': len(sequences.shape) == 3 and sequences.size > 0,
            'metadata_count': len(metadata) if metadata else 0
        }
        
        print(f"✅ Enhanced sequences created:")
        print(f"   Shape: {sequences.shape}")
        print(f"   Size: {sequences.size}")
        print(f"   Valid: {results['enhanced_sequences']['is_valid']}")
        print(f"   Metadata entries: {len(metadata) if metadata else 0}")
        
        if sequences.size == 0:
            print("❌ WARNING: Enhanced preprocessing returned empty sequences!")
            print("   This will cause tuple unpacking errors later")
            
            # Analyze why sequences are empty
            print("\n🔍 Analyzing why sequences are empty...")
            for well in df['WELL'].unique():
                well_df = df[df['WELL'] == well]
                print(f"   {well}: {len(well_df)} rows")
                
                # Check for continuous data
                is_valid = well_df[all_features].notna().all(axis=1)
                continuous_count = is_valid.sum()
                print(f"     Continuous valid rows: {continuous_count}")
                
                if continuous_count < sequence_len:
                    print(f"     ❌ Insufficient continuous data ({continuous_count} < {sequence_len})")
                else:
                    print(f"     ✅ Sufficient continuous data")
        
    except Exception as e:
        results['enhanced_sequences'] = {'error': str(e)}
        print(f"❌ Enhanced sequence creation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 3: Test missing value introduction (if sequences were created)
    if 'enhanced_sequences' in results and results['enhanced_sequences'].get('is_valid', False):
        print(f"\n❓ Step 3: Testing missing value introduction...")
        try:
            sequences_with_missing = preprocessor.introduce_realistic_missingness(sequences)
            
            results['missing_introduction'] = {
                'shape': sequences_with_missing.shape,
                'size': sequences_with_missing.size,
                'dtype': sequences_with_missing.dtype,
                'is_valid': len(sequences_with_missing.shape) == 3 and sequences_with_missing.size > 0
            }
            
            print(f"✅ Missing values introduced:")
            print(f"   Shape: {sequences_with_missing.shape}")
            print(f"   Size: {sequences_with_missing.size}")
            
        except Exception as e:
            results['missing_introduction'] = {'error': str(e)}
            print(f"❌ Missing value introduction failed: {e}")
    
    # Step 4: Test tuple unpacking scenarios
    print(f"\n🔧 Step 4: Testing tuple unpacking scenarios...")
    
    # Test the problematic unpacking patterns
    test_cases = [
        ("Empty array", np.array([])),
        ("1D array", np.array([1, 2, 3])),
        ("2D array", np.array([[1, 2], [3, 4]])),
        ("3D array", np.array([[[1, 2, 3], [4, 5, 6]]])),
        ("Valid sequences", sequences if 'sequences' in locals() and sequences.size > 0 else np.array([[[1, 2, 3], [4, 5, 6]]]))
    ]
    
    unpacking_results = {}
    
    for test_name, test_array in test_cases:
        try:
            if len(test_array.shape) == 3:
                n_sequences, seq_len, n_features = test_array.shape
                unpacking_results[test_name] = f"✅ SUCCESS: {n_sequences}, {seq_len}, {n_features}"
            else:
                unpacking_results[test_name] = f"❌ FAIL: Shape {test_array.shape} not 3D"
        except ValueError as e:
            unpacking_results[test_name] = f"❌ FAIL: {e}"
    
    results['unpacking_tests'] = unpacking_results
    
    for test_name, result in unpacking_results.items():
        print(f"   {test_name}: {result}")
    
    return results


def generate_fixes_for_issues(debug_results: Dict[str, Any]) -> List[str]:
    """Generate specific fixes based on debug results."""
    fixes = []
    
    if not debug_results.get('enhanced_available', False):
        fixes.append("🔧 FIX 1: Install enhanced preprocessing module")
        fixes.append("   pip install enhanced_preprocessing")
        return fixes
    
    enhanced_seq = debug_results.get('enhanced_sequences', {})
    
    if enhanced_seq.get('size', 0) == 0:
        fixes.append("🔧 FIX 1: Enhanced preprocessing returns empty sequences")
        fixes.append("   • Reduce sequence_len (try 16, 8, or 4)")
        fixes.append("   • Check data quality - ensure continuous data segments")
        fixes.append("   • Use fallback to standard preprocessing")
        fixes.append("   • Consider data augmentation for very small datasets")
    
    if not enhanced_seq.get('is_valid', False):
        fixes.append("🔧 FIX 2: Invalid sequence format from enhanced preprocessing")
        fixes.append("   • Enhanced preprocessing should return 3D arrays")
        fixes.append("   • Check EnhancedLogPreprocessor configuration")
        fixes.append("   • Verify input data format and column names")
    
    unpacking_issues = any("FAIL" in result for result in debug_results.get('unpacking_tests', {}).values())
    if unpacking_issues:
        fixes.append("🔧 FIX 3: Tuple unpacking errors")
        fixes.append("   • Add shape validation before unpacking")
        fixes.append("   • Handle empty arrays gracefully")
        fixes.append("   • Implement fallback mechanisms")
    
    if 'error' in enhanced_seq:
        fixes.append("🔧 FIX 4: Enhanced preprocessing exceptions")
        fixes.append(f"   • Error: {enhanced_seq['error']}")
        fixes.append("   • Check input data compatibility")
        fixes.append("   • Verify enhanced preprocessing installation")
    
    return fixes


def test_fixes_applied():
    """Test that the fixes have been applied correctly."""
    print("\n🧪 TESTING APPLIED FIXES")
    print("=" * 60)
    
    fixes_status = {}
    
    # Test 1: Shape validation before unpacking
    print("1. Testing shape validation before unpacking...")
    try:
        test_arrays = [
            np.array([]),           # Empty
            np.array([1, 2, 3]),    # 1D
            np.array([[[1, 2, 3], [4, 5, 6]]])  # 3D
        ]
        
        for i, arr in enumerate(test_arrays):
            if arr.size == 0 or len(arr.shape) != 3:
                print(f"   Array {i}: Invalid shape {arr.shape} - would skip")
            else:
                n_seq, seq_len, n_feat = arr.shape
                print(f"   Array {i}: Valid shape {arr.shape} - unpacked successfully")
        
        fixes_status['shape_validation'] = "✅ WORKING"
        
    except Exception as e:
        fixes_status['shape_validation'] = f"❌ FAILED: {e}"
    
    # Test 2: Fallback mechanism
    print("\n2. Testing fallback mechanism...")
    try:
        # Simulate the fallback logic
        def test_fallback(sequences):
            if sequences.size == 0 or len(sequences.shape) != 3:
                print("   Fallback triggered - would use original function")
                return "fallback"
            else:
                print("   Normal processing - would continue with optimized function")
                return "optimized"
        
        result1 = test_fallback(np.array([]))  # Should trigger fallback
        result2 = test_fallback(np.array([[[1, 2, 3]]]))  # Should continue normally
        
        if result1 == "fallback" and result2 == "optimized":
            fixes_status['fallback_mechanism'] = "✅ WORKING"
        else:
            fixes_status['fallback_mechanism'] = "❌ LOGIC ERROR"
            
    except Exception as e:
        fixes_status['fallback_mechanism'] = f"❌ FAILED: {e}"
    
    return fixes_status


def main():
    """Main diagnostic function."""
    print("🚀 ENHANCED PREPROCESSING DEBUG TOOL")
    print("=" * 80)
    
    # Create sample data for testing
    print("📊 Creating sample test data...")
    np.random.seed(42)
    
    sample_data = []
    for i in range(3):  # 3 small wells
        well_name = f'WELL_{i}'
        n_rows = np.random.randint(20, 40)  # Small wells
        
        well_df = pd.DataFrame({
            'WELL': [well_name] * n_rows,
            'MD': np.arange(n_rows) * 0.5,
            'GR': np.random.normal(50, 20, n_rows),
            'NPHI': np.random.normal(0.2, 0.1, n_rows),
            'RHOB': np.random.normal(2.3, 0.3, n_rows),
            'DT': np.random.normal(100, 30, n_rows)
        })
        sample_data.append(well_df)
    
    test_df = pd.concat(sample_data, ignore_index=True)
    
    # Add some missing values
    missing_mask = np.random.random(test_df.shape) < 0.1
    test_df = test_df.mask(missing_mask)
    
    print(f"✅ Sample data created: {len(test_df)} rows, {len(test_df['WELL'].unique())} wells")
    
    # Run diagnostics
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'DT'
    
    debug_results = debug_enhanced_preprocessing_chain(test_df, feature_cols, target_col, sequence_len=16)
    
    # Generate fixes
    fixes = generate_fixes_for_issues(debug_results)
    
    # Test applied fixes
    fixes_status = test_fixes_applied()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 80)
    
    print("🔍 ISSUES FOUND:")
    if fixes:
        for fix in fixes:
            print(f"   {fix}")
    else:
        print("   ✅ No major issues detected")
    
    print(f"\n🧪 FIXES STATUS:")
    for fix_name, status in fixes_status.items():
        print(f"   • {fix_name}: {status}")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. The tuple unpacking errors should now be fixed")
    print(f"   2. Enhanced preprocessing will fall back to standard if it fails")
    print(f"   3. Shape validation prevents crashes from empty sequences")
    print(f"   4. Try Option 3 again - it should work or provide better error messages")


if __name__ == "__main__":
    main()
