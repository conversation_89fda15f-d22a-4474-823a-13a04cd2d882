import os
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox
from preprocessing.ml_core import MODEL_REGISTRY

AUTO_MODE = False

def console_select(options, prompt, default=None, multiple=False):
    print(f"\n{prompt}")
    for i,opt in enumerate(options,1):
        print(f"  {i}. {opt}")
    if default:
        print(f"Default: {default}")
    if AUTO_MODE:
        return default if not multiple else (default if isinstance(default, list) else [default])
    while True:
        choice = input("Selection: ").strip()
        if not choice and default:
            return default if not multiple else (default if isinstance(default, list) else [default])
        if multiple and choice.lower()=='all':
            return options
        try:
            idxs = [int(x) for x in choice.split(',')]
            sel = [options[i-1] for i in idxs]
            return sel if multiple else sel[0]
        except:
            print("Invalid input.")

def select_las_files_dialog():
    """Open a file dialog to select multiple LAS files."""
    # Hide the main tkinter window
    root = tk.Tk()
    root.withdraw()

    # Open file dialog for multiple LAS files
    file_paths = filedialog.askopenfilenames(
        title="Select LAS Files",
        filetypes=[
            ("LAS files", "*.las"),
            ("All files", "*.*")
        ],
        multiple=True
    )

    root.destroy()

    if not file_paths:
        print("No files selected.")
        return []

    print(f"Selected {len(file_paths)} LAS files:")
    for i, path in enumerate(file_paths, 1):
        print(f"  {i}. {os.path.basename(path)}")

    return list(file_paths)

def get_io_paths():
    """Get input and output paths. Offers choice between directory or file selection."""
    print("\nChoose input method:")
    print("1. Select directory containing LAS files")
    print("2. Select individual LAS files (GUI dialog)")

    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            inp = input("Input LAS directory: ").strip()
            break
        elif choice == "2":
            print("Opening file selection dialog...")
            selected_files = select_las_files_dialog()
            if selected_files:
                inp = selected_files  # Return list of file paths instead of directory
                break
            else:
                print("No files selected. Please try again.")
                continue
        else:
            print("Invalid choice. Please enter 1 or 2.")

    out = input("Output directory: ").strip()
    return inp, out

def get_input_files():
    """Get input LAS files using file dialog."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None

    return selected_files

def select_output_directory():
    """Open a directory dialog to select output directory."""
    root = tk.Tk()
    root.withdraw()

    output_dir = filedialog.askdirectory(
        title="Select Output Directory"
    )

    root.destroy()

    if not output_dir:
        print("No output directory selected.")
        return None

    print(f"Selected output directory: {output_dir}")
    return output_dir

def get_io_paths_simple():
    """Simple version that directly opens file dialog for LAS selection."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None, None

    out = input("Output directory: ").strip()
    return selected_files, out

def configure_log_selection(logs):
    feats = console_select(logs, "Select feature logs (comma separated indexes)", multiple=True, default=logs[:4])
    tlist = [l for l in logs if l not in feats]
    tgt = console_select(tlist, "Select target log", default=tlist[0])
    return feats, tgt

def configure_well_separation(wells):
    mode = console_select(['mixed','separated'], "Training/prediction mode?", default='mixed')
    if mode=='mixed':
        return {'mode':'mixed','training_wells':wells,'prediction_wells':wells}
    tr = console_select(wells, "Training wells", multiple=True)
    pr = [w for w in wells if w not in tr]
    pr = console_select(pr, "Prediction wells", multiple=True, default=pr)
    return {'mode':'separated','training_wells':tr,'prediction_wells':pr}

def get_prediction_mode():
    mode = console_select(['1','2','3'], "Prediction mode: 1 fill-missing, 2 CV, 3 full", default='1')
    return int(mode)

def configure_deep_learning_pipeline():
    """
    Configure the deep learning pipeline optimization settings.

    Returns:
        Dict containing pipeline configuration options
    """
    print("\n🚀 Deep Learning Pipeline Configuration")
    print("=" * 60)
    print("Choose the training pipeline for SAITS/BRITS models:")
    print("")
    print("1. 🔥 Optimized Phase 1 Pipeline (RECOMMENDED)")
    print("   • 3-4x faster training with moderate optimization")
    print("   • Advanced preprocessing and validation")
    print("   • GPU-accelerated operations when available")
    print("   • Automatic fallback to original pipeline if issues occur")
    print("")
    print("2. 📚 Original Training Pipeline")
    print("   • Standard training path from ml_core.py")
    print("   • Proven stability and compatibility")
    print("   • No additional optimizations")
    print("")
    print("3. ⚡ Maximum Performance Pipeline (EXPERIMENTAL)")
    print("   • 4-6x faster training with aggressive optimization")
    print("   • GPU preprocessing and tensor operations")
    print("   • Best for large datasets and modern hardware")
    print("")

    while True:
        choice = input("Select pipeline (1-3) [1]: ").strip()
        if not choice:
            choice = "1"

        if choice == "1":
            return {
                'use_optimized_pipeline': True,
                'optimization_level': 'moderate',
                'pipeline_name': 'Optimized Phase 1',
                'expected_speedup': '3-4x',
                'description': 'Balanced optimization with safety fallbacks'
            }
        elif choice == "2":
            return {
                'use_optimized_pipeline': False,
                'optimization_level': None,
                'pipeline_name': 'Original',
                'expected_speedup': '1x (baseline)',
                'description': 'Standard training without optimizations'
            }
        elif choice == "3":
            print("\n⚠️ IMPORTANT: Maximum Performance Pipeline Requirements")
            print("   • Best for datasets with >1000 rows and >50 rows per well")
            print("   • May auto-adjust to moderate optimization for small datasets")
            print("   • Includes automatic fallback mechanisms")
            print("   • GPU acceleration enabled when available")

            confirm = input("\nContinue with Maximum Performance Pipeline? (y/n) [y]: ").strip().lower()
            if confirm and confirm != 'y':
                print("Returning to pipeline selection...")
                continue

            return {
                'use_optimized_pipeline': True,
                'optimization_level': 'aggressive',
                'pipeline_name': 'Maximum Performance',
                'expected_speedup': '4-6x',
                'description': 'Aggressive optimization with adaptive fallbacks'
            }
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")


def configure_gpu_optimization():
    """
    Configure GPU-specific optimization settings based on hardware capabilities.

    Returns:
        Dict containing GPU optimization configuration
    """
    import torch

    gpu_config = {
        'mixed_precision_enabled': False,
        'gpu_preprocessing_enabled': False,
        'compute_capability': None,
        'optimization_strategy': 'cpu'
    }

    if not torch.cuda.is_available():
        print("💻 CUDA not available - using CPU optimizations")
        return gpu_config

    # Detect GPU compute capability
    try:
        capability = torch.cuda.get_device_capability()
        gpu_name = torch.cuda.get_device_name()
        gpu_config['compute_capability'] = f"{capability[0]}.{capability[1]}"

        print(f"\n🎯 GPU Detected: {gpu_name}")
        print(f"   Compute Capability: {capability[0]}.{capability[1]}")

        if capability[0] >= 7:
            # Volta/Turing/Ampere - supports mixed precision
            gpu_config['mixed_precision_enabled'] = True
            gpu_config['gpu_preprocessing_enabled'] = True
            gpu_config['optimization_strategy'] = 'modern_gpu'
            print("   ✅ Mixed precision training supported")
            print("   ✅ GPU preprocessing enabled")
        elif capability[0] == 6:
            # Pascal - disable mixed precision, enable GPU preprocessing
            gpu_config['mixed_precision_enabled'] = False
            gpu_config['gpu_preprocessing_enabled'] = True
            gpu_config['optimization_strategy'] = 'pascal_gpu'
            print("   ⚠️ Mixed precision disabled (Pascal GPU - better performance with FP32)")
            print("   ✅ GPU preprocessing enabled (FP32 optimized)")
        else:
            # Older GPUs - CPU optimizations only
            gpu_config['optimization_strategy'] = 'cpu'
            print("   ⚠️ GPU too old for optimizations - using CPU path")

    except Exception as e:
        print(f"   ⚠️ GPU detection failed: {e}")

    return gpu_config


def apply_data_sufficiency_optimizations(hparams, df, feature_cols, target_col):
    """
    Apply data sufficiency optimizations to hyperparameters based on actual data.

    Args:
        hparams: Dictionary of hyperparameters for all models
        df: Input dataframe for analysis
        feature_cols: List of feature columns
        target_col: Target column name

    Returns:
        Updated hyperparameters optimized for data sufficiency
    """
    optimized_hparams = hparams.copy()

    # Analyze data characteristics
    wells = df['WELL'].unique()
    all_features = feature_cols + [target_col]

    # Calculate well statistics
    well_sizes = []
    max_continuous_intervals = []

    for well in wells:
        well_df = df[df['WELL'] == well]
        well_sizes.append(len(well_df))

        # Find continuous intervals
        is_valid = well_df[all_features].notna().all(axis=1)
        if is_valid.any():
            # Find continuous segments
            valid_changes = np.diff(is_valid.astype(int))
            interval_edges = np.where(valid_changes != 0)[0] + 1

            intervals = []
            if is_valid.iloc[0]:
                intervals.append([0])

            for edge in interval_edges:
                if len(intervals) > 0 and len(intervals[-1]) == 1:
                    intervals[-1].append(edge)
                else:
                    intervals.append([edge])

            if len(intervals) > 0 and len(intervals[-1]) == 1:
                intervals[-1].append(len(well_df))

            interval_lengths = [end - start for start, end in intervals]
            max_continuous_intervals.append(max(interval_lengths) if interval_lengths else 0)
        else:
            max_continuous_intervals.append(0)

    # Calculate optimal sequence length
    if max_continuous_intervals:
        # Use 75th percentile of maximum continuous intervals, but cap at reasonable limits
        optimal_seq_len = int(np.percentile([x for x in max_continuous_intervals if x > 0], 75))
        optimal_seq_len = max(16, min(optimal_seq_len, 64))  # Between 16 and 64
    else:
        optimal_seq_len = 32  # Conservative default

    # Calculate data sufficiency metrics
    median_well_size = np.median(well_sizes) if well_sizes else 0
    small_wells_ratio = sum(1 for size in well_sizes if size < 50) / len(well_sizes) if well_sizes else 1

    print(f"\n📊 Data Sufficiency Analysis:")
    print(f"   • Total wells: {len(wells)}")
    print(f"   • Median well size: {median_well_size:.0f} rows")
    print(f"   • Small wells (< 50 rows): {small_wells_ratio:.1%}")
    print(f"   • Optimal sequence length: {optimal_seq_len}")

    # Apply optimizations to deep learning models
    deep_models = ['saits', 'brits', 'transformer', 'mrnn']

    for model_key in deep_models:
        if model_key in optimized_hparams:
            model_params = optimized_hparams[model_key].copy()
            original_seq_len = model_params.get('sequence_len', 64)

            # Adjust sequence length if current setting is too large
            if original_seq_len > optimal_seq_len:
                model_params['sequence_len'] = optimal_seq_len
                print(f"   📏 {model_key}: Sequence length adjusted: {original_seq_len} → {optimal_seq_len}")

            # Special handling for very small datasets
            if small_wells_ratio > 0.7 and median_well_size < 40:
                # Force smaller sequence length for very small datasets
                emergency_seq_len = max(8, min(16, optimal_seq_len))
                if model_params['sequence_len'] > emergency_seq_len:
                    model_params['sequence_len'] = emergency_seq_len
                    print(f"   🚨 {model_key}: Emergency sequence length for tiny dataset: → {emergency_seq_len}")

            # Adjust batch size for small datasets
            if small_wells_ratio > 0.5:  # More than 50% small wells
                current_batch = model_params.get('batch_size', 32)
                smaller_batch = max(8, current_batch // 2)  # Reduce batch size, minimum 8
                model_params['batch_size'] = smaller_batch
                print(f"   📦 {model_key}: Batch size reduced for small dataset: {current_batch} → {smaller_batch}")

            # Reduce epochs for very small datasets to prevent overfitting
            if median_well_size < 30:
                current_epochs = model_params.get('epochs', 50)
                reduced_epochs = max(20, current_epochs // 2)
                model_params['epochs'] = reduced_epochs
                print(f"   ⏱️ {model_key}: Epochs reduced for small dataset: {current_epochs} → {reduced_epochs}")

            optimized_hparams[model_key] = model_params

    return optimized_hparams


def apply_gpu_optimizations_to_hparams(hparams, gpu_config):
    """
    Apply GPU-specific optimizations to hyperparameters.

    Args:
        hparams: Dictionary of hyperparameters for all models
        gpu_config: GPU configuration from configure_gpu_optimization()

    Returns:
        Updated hyperparameters with GPU optimizations applied
    """
    optimized_hparams = hparams.copy()

    # Apply optimizations to deep learning models
    deep_models = ['saits', 'brits', 'transformer', 'mrnn']

    for model_key in deep_models:
        if model_key in optimized_hparams:
            model_params = optimized_hparams[model_key].copy()

            # Disable mixed precision for Pascal GPUs (compute capability 6.x)
            if gpu_config['optimization_strategy'] == 'pascal_gpu':
                model_params['use_mixed_precision'] = False
                print(f"   🔧 {model_key}: Mixed precision disabled for Pascal GPU")
            elif gpu_config['optimization_strategy'] == 'modern_gpu':
                model_params['use_mixed_precision'] = True
                print(f"   ⚡ {model_key}: Mixed precision enabled for modern GPU")
            else:
                model_params['use_mixed_precision'] = False
                print(f"   💻 {model_key}: Using CPU optimizations")

            # Optimize batch size for Pascal GPUs (better FP32 throughput with larger batches)
            if gpu_config['optimization_strategy'] == 'pascal_gpu':
                # Increase batch size for Pascal GPUs (good FP32 performance)
                current_batch = model_params.get('batch_size', 128)
                optimized_batch = min(current_batch * 2, 256)  # Cap at 256
                model_params['batch_size'] = optimized_batch
                print(f"   📊 {model_key}: Batch size optimized for Pascal: {current_batch} → {optimized_batch}")

            # Add GPU configuration metadata
            model_params['gpu_config'] = gpu_config

            optimized_hparams[model_key] = model_params

    return optimized_hparams


def configure_hyperparameters():
    params = {}
    for k,m in MODEL_REGISTRY.items():
        params[k] = {**m['fixed_params']}
        for p,meta in m['hyperparameters'].items():
            params[k][p] = meta['default']
    return params


def get_small_dataset_config():
    """
    Get optimized configuration for small datasets with insufficient data issues.

    Returns:
        Dictionary with small dataset optimized hyperparameters
    """
    return {
        'saits': {
            'sequence_len': 16,  # Much smaller sequences
            'n_features': 4,
            'n_layers': 1,  # Simpler model
            'd_model': 128,  # Smaller model dimension
            'n_heads': 2,  # Fewer attention heads
            'epochs': 30,  # Fewer epochs to prevent overfitting
            'batch_size': 8,  # Smaller batches
            'learning_rate': 1e-3,
            'dropout': 0.2,  # Higher dropout for regularization
            'device': None,
            'use_mixed_precision': False,  # Disable for stability
        },
        'brits': {
            'sequence_len': 16,  # Much smaller sequences
            'n_features': 4,
            'rnn_hidden_size': 64,  # Smaller hidden size
            'epochs': 30,
            'batch_size': 8,
            'learning_rate': 1e-3,
            'device': None,
            'use_mixed_precision': False,
        }
    }


def apply_small_dataset_fallback(hparams, insufficient_wells_ratio=0.8):
    """
    Apply small dataset configuration if most wells are insufficient.

    Args:
        hparams: Current hyperparameters
        insufficient_wells_ratio: Threshold for applying small dataset config

    Returns:
        Updated hyperparameters for small datasets
    """
    small_config = get_small_dataset_config()

    print(f"\n🔧 Applying small dataset configuration:")
    print(f"   • Sequence length: 64 → 16")
    print(f"   • Batch size: 32 → 8")
    print(f"   • Model complexity: Reduced")
    print(f"   • Mixed precision: Disabled")

    # Update deep learning models with small dataset config
    for model_key in ['saits', 'brits']:
        if model_key in hparams and model_key in small_config:
            hparams[model_key].update(small_config[model_key])

    return hparams
