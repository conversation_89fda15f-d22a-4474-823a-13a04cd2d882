#!/usr/bin/env python3
"""
Diagnostic script to analyze data insufficiency issues for SAITS/BRITS training.

This script helps identify why wells are being marked as having "insufficient data"
and provides recommendations for resolving the issue.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any


def diagnose_well_data_sufficiency(df: pd.DataFrame, 
                                  feature_cols: List[str],
                                  target_col: str,
                                  sequence_len: int = 64,
                                  val_depth_ratio: float = 0.3,
                                  well_col: str = 'WELL',
                                  depth_col: str = 'MD') -> Dict[str, Any]:
    """
    Comprehensive diagnosis of data sufficiency issues.
    
    Args:
        df: Input dataframe
        feature_cols: List of feature column names
        target_col: Target column name
        sequence_len: Required sequence length for deep learning models
        val_depth_ratio: Validation split ratio
        well_col: Well identifier column
        depth_col: Depth column
        
    Returns:
        Dictionary with detailed diagnostic information
    """
    
    print("🔍 SAITS/BRITS Data Sufficiency Diagnostic")
    print("=" * 60)
    
    wells = df[well_col].unique()
    all_features = feature_cols + [target_col]
    
    # Calculate minimum required data points
    min_for_validation = int(sequence_len / (1 - val_depth_ratio))  # ~91 for seq_len=64, val_ratio=0.3
    
    print(f"📊 Data Requirements Analysis:")
    print(f"   • Sequence length: {sequence_len}")
    print(f"   • Validation ratio: {val_depth_ratio:.1%}")
    print(f"   • Minimum well size (basic): 20 data points")
    print(f"   • Minimum well size (effective): {min_for_validation} data points")
    print(f"   • Total wells: {len(wells)}")
    
    well_diagnostics = {}
    sufficient_wells = []
    insufficient_wells = []
    
    for well in wells:
        well_df = df[df[well_col] == well].sort_values(depth_col)
        
        # Basic statistics
        total_rows = len(well_df)
        
        # Check for missing values in each column
        missing_stats = {}
        for col in all_features:
            if col in well_df.columns:
                missing_count = well_df[col].isna().sum()
                missing_rate = missing_count / total_rows if total_rows > 0 else 1.0
                missing_stats[col] = {
                    'missing_count': missing_count,
                    'missing_rate': missing_rate,
                    'valid_count': total_rows - missing_count
                }
        
        # Check continuous intervals (no NaNs in any feature)
        is_valid = well_df[all_features].notna().all(axis=1)
        valid_changes = np.diff(is_valid.astype(int))
        interval_edges = np.where(valid_changes != 0)[0] + 1
        
        # Build list of valid intervals
        intervals = []
        if is_valid.iloc[0]:
            intervals.append([0])
        
        for edge in interval_edges:
            if len(intervals) > 0 and len(intervals[-1]) == 1:
                intervals[-1].append(edge)
            else:
                intervals.append([edge])
        
        if len(intervals) > 0 and len(intervals[-1]) == 1:
            intervals[-1].append(len(well_df))
        
        # Calculate interval statistics
        interval_lengths = [end - start for start, end in intervals]
        usable_intervals = [length for length in interval_lengths if length >= sequence_len]
        
        # Determine sufficiency
        passes_basic_check = total_rows >= 20
        passes_sequence_check = len(usable_intervals) > 0
        passes_validation_check = total_rows >= min_for_validation
        
        is_sufficient = passes_basic_check and passes_sequence_check and passes_validation_check
        
        well_diagnostics[well] = {
            'total_rows': total_rows,
            'missing_stats': missing_stats,
            'valid_continuous_rows': sum(interval_lengths),
            'intervals': intervals,
            'interval_lengths': interval_lengths,
            'usable_intervals': usable_intervals,
            'max_usable_interval': max(interval_lengths) if interval_lengths else 0,
            'passes_basic_check': passes_basic_check,
            'passes_sequence_check': passes_sequence_check,
            'passes_validation_check': passes_validation_check,
            'is_sufficient': is_sufficient,
            'limiting_factor': get_limiting_factor(passes_basic_check, passes_sequence_check, passes_validation_check)
        }
        
        if is_sufficient:
            sufficient_wells.append(well)
        else:
            insufficient_wells.append(well)
    
    # Print detailed diagnostics
    print(f"\n📈 Well-by-Well Analysis:")
    print(f"   • Sufficient wells: {len(sufficient_wells)}")
    print(f"   • Insufficient wells: {len(insufficient_wells)}")
    
    if insufficient_wells:
        print(f"\n❌ Insufficient Wells Analysis:")
        for well in insufficient_wells:
            diag = well_diagnostics[well]
            print(f"   {well}:")
            print(f"     • Total rows: {diag['total_rows']}")
            print(f"     • Max continuous interval: {diag['max_usable_interval']}")
            print(f"     • Usable intervals (≥{sequence_len}): {len(diag['usable_intervals'])}")
            print(f"     • Limiting factor: {diag['limiting_factor']}")
            
            # Show missing data breakdown
            for col, stats in diag['missing_stats'].items():
                if stats['missing_rate'] > 0:
                    print(f"     • {col}: {stats['missing_rate']:.1%} missing ({stats['valid_count']} valid)")
    
    if sufficient_wells:
        print(f"\n✅ Sufficient Wells:")
        for well in sufficient_wells:
            diag = well_diagnostics[well]
            print(f"   {well}: {diag['total_rows']} rows, {len(diag['usable_intervals'])} usable intervals")
    
    # Generate recommendations
    recommendations = generate_recommendations(well_diagnostics, sequence_len, val_depth_ratio)
    
    return {
        'well_diagnostics': well_diagnostics,
        'sufficient_wells': sufficient_wells,
        'insufficient_wells': insufficient_wells,
        'recommendations': recommendations,
        'summary': {
            'total_wells': len(wells),
            'sufficient_count': len(sufficient_wells),
            'insufficient_count': len(insufficient_wells),
            'success_rate': len(sufficient_wells) / len(wells) if wells else 0
        }
    }


def get_limiting_factor(passes_basic: bool, passes_sequence: bool, passes_validation: bool) -> str:
    """Identify the primary limiting factor for data insufficiency."""
    if not passes_basic:
        return "Basic threshold (< 20 rows)"
    elif not passes_sequence:
        return "No continuous intervals ≥ sequence_len"
    elif not passes_validation:
        return "Insufficient for train/validation split"
    else:
        return "Sufficient"


def generate_recommendations(well_diagnostics: Dict[str, Any], 
                           sequence_len: int, 
                           val_depth_ratio: float) -> List[str]:
    """Generate specific recommendations based on diagnostic results."""
    recommendations = []
    
    insufficient_wells = [well for well, diag in well_diagnostics.items() if not diag['is_sufficient']]
    
    if not insufficient_wells:
        recommendations.append("✅ All wells have sufficient data for current configuration")
        return recommendations
    
    # Analyze common issues
    basic_failures = sum(1 for well in insufficient_wells if not well_diagnostics[well]['passes_basic_check'])
    sequence_failures = sum(1 for well in insufficient_wells if not well_diagnostics[well]['passes_sequence_check'])
    validation_failures = sum(1 for well in insufficient_wells if not well_diagnostics[well]['passes_validation_check'])
    
    recommendations.append(f"🔧 CONFIGURATION ADJUSTMENTS NEEDED:")
    
    if sequence_failures > 0:
        max_intervals = [max(diag['interval_lengths']) if diag['interval_lengths'] else 0 
                        for diag in well_diagnostics.values()]
        suggested_seq_len = max(16, min(32, int(np.percentile([x for x in max_intervals if x > 0], 75))))
        
        recommendations.append(f"1. 📏 REDUCE SEQUENCE LENGTH:")
        recommendations.append(f"   • Current: {sequence_len} → Suggested: {suggested_seq_len}")
        recommendations.append(f"   • This will help {sequence_failures} wells with fragmented data")
        recommendations.append(f"   • Update hyperparameters: hparams['sequence_len'] = {suggested_seq_len}")
    
    if validation_failures > 0:
        recommendations.append(f"2. 📊 ADJUST VALIDATION SPLIT:")
        recommendations.append(f"   • Current validation ratio: {val_depth_ratio:.1%}")
        recommendations.append(f"   • Suggested: 0.2 (20%) or 0.15 (15%)")
        recommendations.append(f"   • This affects {validation_failures} wells")
        recommendations.append(f"   • Update: val_depth_ratio=0.2 in create_flexible_split()")
    
    if basic_failures > 0:
        recommendations.append(f"3. 🗂️ DATA QUALITY ISSUES:")
        recommendations.append(f"   • {basic_failures} wells have < 20 total rows")
        recommendations.append(f"   • Consider data cleaning or well exclusion")
        recommendations.append(f"   • Check for data loading issues")
    
    # Alternative approaches
    recommendations.append(f"4. 🔄 ALTERNATIVE APPROACHES:")
    recommendations.append(f"   • Use 'conservative' optimization level (more tolerant)")
    recommendations.append(f"   • Enable enhanced preprocessing with gap filling")
    recommendations.append(f"   • Consider well grouping for very small wells")
    
    return recommendations


def main():
    """Example usage of the diagnostic tool."""
    print("This is a diagnostic tool. Import and use with your actual data:")
    print()
    print("from diagnose_data_insufficiency import diagnose_well_data_sufficiency")
    print()
    print("# Example usage:")
    print("results = diagnose_well_data_sufficiency(")
    print("    df=your_dataframe,")
    print("    feature_cols=['GR', 'NPHI', 'RHOB'],")
    print("    target_col='DT',")
    print("    sequence_len=64  # Current SAITS default")
    print(")")
    print()
    print("# Print recommendations:")
    print("for rec in results['recommendations']:")
    print("    print(rec)")


if __name__ == "__main__":
    main()
