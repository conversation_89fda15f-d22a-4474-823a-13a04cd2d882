#!/usr/bin/env python3
"""
Test script for Phase 2 data quality enhancement functions.
This script validates the newly implemented functions in enhanced_preprocessing.py.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from preprocessing.deep.enhanced_preprocessing import (
    validate_and_clean_log_data,
    improve_well_quality,
    adaptive_sequence_creation,
    combine_well_sequences,
    calculate_quality_score
)

def create_sample_well_data(n_samples=1000, add_outliers=True, add_gaps=True):
    """
    Create sample well log data for testing.
    
    Args:
        n_samples: Number of data points
        add_outliers: Whether to add outliers
        add_gaps: Whether to add missing data gaps
    
    Returns:
        DataFrame with sample well log data
    """
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic well log data
    depth = np.arange(n_samples) * 0.1  # 0.1m intervals
    
    # Gamma Ray (GR) - typically 0-300 API units
    gr = 50 + 30 * np.sin(depth * 0.01) + np.random.normal(0, 10, n_samples)
    
    # Neutron Porosity (NPHI) - typically -0.15 to 1.0
    nphi = 0.2 + 0.1 * np.cos(depth * 0.02) + np.random.normal(0, 0.05, n_samples)
    
    # Resistivity (RT) - log-normal distribution
    rt = np.exp(2 + 0.5 * np.sin(depth * 0.005) + np.random.normal(0, 0.3, n_samples))
    
    # Create DataFrame
    data = pd.DataFrame({
        'DEPTH': depth,
        'GR': gr,
        'NPHI': nphi,
        'RT': rt
    })
    
    if add_outliers:
        # Add some outliers
        outlier_indices = np.random.choice(n_samples, size=20, replace=False)
        data.loc[outlier_indices[:10], 'GR'] = np.random.uniform(400, 500, 10)  # Out of range
        data.loc[outlier_indices[10:], 'NPHI'] = np.random.uniform(1.5, 2.0, 10)  # Out of range
    
    if add_gaps:
        # Add some missing data gaps
        gap_indices = np.random.choice(n_samples, size=50, replace=False)
        data.loc[gap_indices, 'RT'] = np.nan
        
        # Add a larger gap
        data.loc[500:510, 'NPHI'] = np.nan
    
    return data

def test_validate_and_clean_log_data():
    """
    Test the validate_and_clean_log_data function.
    """
    print("\n=== Testing validate_and_clean_log_data ===")
    
    # Create sample data with outliers
    data = create_sample_well_data(n_samples=500, add_outliers=True, add_gaps=False)
    
    # Define log ranges
    log_ranges = {
        'GR': (0, 300),
        'NPHI': (-0.15, 1.0),
        'RT': (0.1, 1000)
    }
    
    print(f"Original data shape: {data.shape}")
    print(f"GR range: {data['GR'].min():.2f} to {data['GR'].max():.2f}")
    print(f"NPHI range: {data['NPHI'].min():.3f} to {data['NPHI'].max():.3f}")
    
    # Clean the data
    cleaned_data = validate_and_clean_log_data(data, log_ranges)
    
    print(f"\nCleaned data shape: {cleaned_data.shape}")
    print(f"Cleaned GR range: {cleaned_data['GR'].min():.2f} to {cleaned_data['GR'].max():.2f}")
    print(f"Cleaned NPHI range: {cleaned_data['NPHI'].min():.3f} to {cleaned_data['NPHI'].max():.3f}")
    
    # Verify ranges are within bounds
    assert cleaned_data['GR'].min() >= 0 and cleaned_data['GR'].max() <= 300
    assert cleaned_data['NPHI'].min() >= -0.15 and cleaned_data['NPHI'].max() <= 1.0
    
    print("✓ validate_and_clean_log_data test passed!")
    return cleaned_data

def test_improve_well_quality():
    """
    Test the improve_well_quality function.
    """
    print("\n=== Testing improve_well_quality ===")
    
    # Create sample data with quality issues
    data = create_sample_well_data(n_samples=300, add_outliers=True, add_gaps=True)
    
    print(f"Original data shape: {data.shape}")
    initial_score = calculate_quality_score(data)
    print(f"Initial quality score: {initial_score:.3f}")
    
    # Improve quality
    improved_data = improve_well_quality(data, target_score=0.7)
    
    final_score = calculate_quality_score(improved_data)
    print(f"Final quality score: {final_score:.3f}")
    
    # Quality should improve or stay the same
    assert final_score >= initial_score - 0.01  # Allow small tolerance
    
    print("✓ improve_well_quality test passed!")
    return improved_data

def test_adaptive_sequence_creation():
    """
    Test the adaptive_sequence_creation function.
    """
    print("\n=== Testing adaptive_sequence_creation ===")
    
    # Create sample data
    data = create_sample_well_data(n_samples=200, add_outliers=False, add_gaps=False)
    
    print(f"Input data shape: {data.shape}")
    
    # Test with different target sequence counts
    for target in [50, 100, 500]:
        print(f"\nTesting with target sequences: {target}")
        sequences = adaptive_sequence_creation(data, target_sequences=target)
        
        print(f"Generated sequences shape: {sequences.shape}")
        
        # Verify sequences have correct structure
        if len(sequences) > 0:
            assert len(sequences.shape) == 3  # (n_sequences, seq_len, n_features)
            assert sequences.shape[2] == 3  # GR, NPHI, RT (excluding DEPTH)
            assert not np.isnan(sequences).any()  # No NaN values
        
        print(f"✓ Generated {len(sequences)} sequences")
    
    print("✓ adaptive_sequence_creation test passed!")
    return sequences

def test_combine_well_sequences():
    """
    Test the combine_well_sequences function.
    """
    print("\n=== Testing combine_well_sequences ===")
    
    # Create multiple wells data
    wells_data = {
        'Well_A': create_sample_well_data(n_samples=150, add_outliers=False, add_gaps=False),
        'Well_B': create_sample_well_data(n_samples=200, add_outliers=False, add_gaps=False),
        'Well_C': create_sample_well_data(n_samples=100, add_outliers=False, add_gaps=False)
    }
    
    print(f"Number of wells: {len(wells_data)}")
    for well_name, well_data in wells_data.items():
        print(f"{well_name}: {well_data.shape[0]} samples")
    
    # Combine sequences
    combined_sequences = combine_well_sequences(wells_data, min_sequences=100)
    
    print(f"\nCombined sequences shape: {combined_sequences.shape}")
    
    # Verify combined sequences
    if len(combined_sequences) > 0:
        assert len(combined_sequences.shape) == 3
        assert combined_sequences.shape[2] == 3  # GR, NPHI, RT
        assert not np.isnan(combined_sequences).any()
        assert len(combined_sequences) >= 100  # Should meet minimum requirement
    
    print("✓ combine_well_sequences test passed!")
    return combined_sequences

def test_quality_score_calculation():
    """
    Test the calculate_quality_score function.
    """
    print("\n=== Testing calculate_quality_score ===")
    
    # Test with perfect data
    perfect_data = create_sample_well_data(n_samples=100, add_outliers=False, add_gaps=False)
    perfect_score = calculate_quality_score(perfect_data)
    print(f"Perfect data quality score: {perfect_score:.3f}")
    
    # Test with problematic data
    problematic_data = create_sample_well_data(n_samples=100, add_outliers=True, add_gaps=True)
    problematic_score = calculate_quality_score(problematic_data)
    print(f"Problematic data quality score: {problematic_score:.3f}")
    
    # Perfect data should have higher score
    assert perfect_score > problematic_score
    assert 0 <= perfect_score <= 1
    assert 0 <= problematic_score <= 1
    
    print("✓ calculate_quality_score test passed!")

def run_all_tests():
    """
    Run all Phase 2 function tests.
    """
    print("Starting Phase 2 Functions Test Suite")
    print("=" * 50)
    
    try:
        # Test individual functions
        cleaned_data = test_validate_and_clean_log_data()
        improved_data = test_improve_well_quality()
        sequences = test_adaptive_sequence_creation()
        combined_sequences = test_combine_well_sequences()
        test_quality_score_calculation()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("Phase 2 data quality enhancement functions are working correctly.")
        
        # Summary
        print("\n=== Test Summary ===")
        print(f"✓ Data validation and cleaning: Working")
        print(f"✓ Quality improvement: Working")
        print(f"✓ Adaptive sequence creation: Working")
        print(f"✓ Multi-well sequence combination: Working")
        print(f"✓ Quality score calculation: Working")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)