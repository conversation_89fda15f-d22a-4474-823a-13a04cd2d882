#!/usr/bin/env python3
"""
Test script to verify the fixes for maximum performance pipeline issues.

Tests:
1. Enhanced sequence creation returns proper 3D empty arrays
2. Optimized training handles shape unpacking safely
3. Fallback mechanisms work correctly
"""

import numpy as np
import pandas as pd
import warnings

def test_empty_array_fix():
    """Test that empty array returns have correct 3D structure."""
    print("Testing Fix 1: Enhanced sequence creation empty array structure")
    
    try:
        from preprocessing.deep.enhanced_preprocessing import EnhancedLogPreprocessor
        
        # Create test data that should result in empty sequences
        # (e.g., data too short for sequence length)
        test_df = pd.DataFrame({
            'WELL': ['W1'] * 10,  # Only 10 rows - too short for sequence_len=64
            'MD': range(10),
            'FEAT1': np.random.randn(10),
            'FEAT2': np.random.randn(10),
            'TARGET': np.random.randn(10)
        })
        
        preprocessor = EnhancedLogPreprocessor(sequence_len=64, sequence_stride=32)
        sequences, metadata = preprocessor.create_sequences_enhanced(
            test_df, 'WELL', ['FEAT1', 'FEAT2', 'TARGET']
        )
        
        print(f"   ✓ Result shape: {sequences.shape}")
        print(f"   ✓ Expected shape structure: (0, 64, 3)")
        print(f"   ✓ Shape dimensions: {len(sequences.shape)}D")
        
        # Check if shape is 3D (adaptive sequence creator may create smaller sequences)
        if len(sequences.shape) == 3:
            if sequences.shape[0] == 0:
                # True empty array case
                print("   PASS: Empty array has correct 3D structure")
                return True
            elif sequences.shape[0] > 0 and sequences.shape[2] == 3:
                # Adaptive fallback created sequences with correct feature count
                print(f"   PASS: Adaptive fallback created valid 3D sequences with {sequences.shape[0]} samples")
                return True
            else:
                print(f"   FAIL: Invalid feature count, expected 3, got {sequences.shape[2]}")
                return False
        else:
            print(f"   FAIL: Expected 3D array, got {len(sequences.shape)}D")
            return False
            
    except Exception as e:
        print(f"   ERROR: {e}")
        return False

def test_shape_unpacking_fix():
    """Test that shape unpacking is safe with validation."""
    print("\nTesting Fix 2: Safe shape unpacking validation")
    
    try:
        from preprocessing.ml_core_phase1_integration import simulate_original_processing
        
        # Test with correct 3D array
        correct_sequences = np.random.randn(10, 64, 5)
        feature_names = ['F1', 'F2', 'F3', 'F4', 'F5']
        
        result = simulate_original_processing(correct_sequences, feature_names)
        print(f"   ✓ 3D array processing: {result.shape}")
        
        # Test with incorrect 1D array (should raise ValueError)
        try:
            incorrect_sequences = np.random.randn(100)  # 1D array
            simulate_original_processing(incorrect_sequences, feature_names)
            print("   FAIL: Should have raised ValueError for 1D array")
            return False
        except ValueError as e:
            print(f"   PASS: Correctly caught invalid shape: {e}")
        
        # Test with incorrect 2D array (should raise ValueError)
        try:
            incorrect_sequences = np.random.randn(10, 64)  # 2D array
            simulate_original_processing(incorrect_sequences, feature_names)
            print("   FAIL: Should have raised ValueError for 2D array")
            return False
        except ValueError as e:
            print(f"   PASS: Correctly caught invalid shape: {e}")
            
        return True
        
    except Exception as e:
        print(f"   ERROR: {e}")
        return False

def test_fallback_compatibility():
    """Test that the fixes maintain compatibility with fallback mechanisms."""
    print("\nTesting Fix 3: Fallback mechanism compatibility")
    
    try:
        from preprocessing.data_handler import create_sequences
        
        # Create test data
        test_df = pd.DataFrame({
            'WELL': ['W1'] * 50 + ['W2'] * 50,
            'MD': list(range(50)) + list(range(50)),
            'FEAT1': np.random.randn(100),
            'FEAT2': np.random.randn(100),
            'TARGET': np.random.randn(100)
        })
        
        # Test enhanced sequence creation with fallback
        sequences, metadata = create_sequences(
            test_df, 'WELL', ['FEAT1', 'FEAT2', 'TARGET'],
            sequence_len=32, step=16, use_enhanced=True
        )
        
        print(f"   ✓ Sequences shape: {sequences.shape}")
        print(f"   ✓ Metadata entries: {len(metadata) if metadata else 0}")
        
        if len(sequences.shape) == 3 and sequences.shape[0] > 0:
            print("   PASS: Fallback mechanism produces valid sequences")
            return True
        else:
            print(f"   FAIL: Invalid sequences shape: {sequences.shape}")
            return False
            
    except Exception as e:
        print(f"   ERROR: {e}")
        return False

def run_all_tests():
    """Run all test cases."""
    print("=" * 60)
    print(" TESTING MAXIMUM PERFORMANCE PIPELINE FIXES")
    print("=" * 60)
    
    results = []
    
    # Test 1: Empty array structure
    results.append(test_empty_array_fix())
    
    # Test 2: Shape unpacking safety
    results.append(test_shape_unpacking_fix())
    
    # Test 3: Fallback compatibility
    results.append(test_fallback_compatibility())
    
    print("\n" + "=" * 60)
    print(" TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("ALL TESTS PASSED - Fixes are working correctly!")
        return True
    else:
        print("SOME TESTS FAILED - Issues need attention!")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)