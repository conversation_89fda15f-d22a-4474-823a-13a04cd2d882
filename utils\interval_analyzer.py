#!/usr/bin/env python3
"""
Interval Analysis Module for SAITS Model

This module provides enhanced interval analysis with detailed logging to diagnose
why sequence creation fails. It identifies valid continuous intervals in well log
data and provides comprehensive diagnostics about data quality and feasibility
for sequence generation.

Key Features:
- Detailed interval detection and analysis
- Comprehensive logging for debugging sequence creation failures
- Data quality assessment with specific recommendations
- Visual representation of data distribution and gaps
- Export capabilities for further analysis
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import warnings
from dataclasses import dataclass
import json
from datetime import datetime


@dataclass
class IntervalInfo:
    """Information about a valid continuous interval."""
    well_name: str
    start_idx: int
    end_idx: int
    length: int
    data_quality: float
    missing_features: List[str]
    feature_completeness: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'well_name': self.well_name,
            'start_idx': self.start_idx,
            'end_idx': self.end_idx,
            'length': self.length,
            'data_quality': self.data_quality,
            'missing_features': self.missing_features,
            'feature_completeness': self.feature_completeness
        }


@dataclass
class IntervalAnalysisReport:
    """Comprehensive interval analysis report."""
    total_wells: int
    total_rows: int
    intervals: List[IntervalInfo]
    overall_quality: float
    recommendations: List[str]
    sequence_feasibility: Dict[int, Dict[str, Any]]
    analysis_timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_wells': self.total_wells,
            'total_rows': self.total_rows,
            'intervals': [interval.to_dict() for interval in self.intervals],
            'overall_quality': self.overall_quality,
            'recommendations': self.recommendations,
            'sequence_feasibility': self.sequence_feasibility,
            'analysis_timestamp': self.analysis_timestamp
        }


class IntervalAnalyzer:
    """Enhanced interval analyzer with detailed logging and diagnostics."""
    
    def __init__(self, verbose: bool = True, export_results: bool = False):
        self.verbose = verbose
        self.export_results = export_results
        self.analysis_history = []
        
    def analyze_intervals(self, df: pd.DataFrame, well_col: str, 
                         feature_cols: List[str]) -> IntervalAnalysisReport:
        """Perform comprehensive interval analysis with detailed logging."""
        
        if self.verbose:
            print("\n🔍 Starting Enhanced Interval Analysis...")
            print(f"   Dataset: {len(df)} rows, {len(df[well_col].unique())} wells")
            print(f"   Features: {feature_cols}")
        
        wells = df[well_col].unique()
        intervals = []
        recommendations = []
        
        # Analyze each well individually
        for well_idx, well in enumerate(wells):
            if self.verbose:
                print(f"\n📊 Analyzing Well {well_idx + 1}/{len(wells)}: {well}")
            
            well_intervals = self._analyze_well_intervals(
                df[df[well_col] == well], well, feature_cols
            )
            intervals.extend(well_intervals)
        
        # Calculate overall statistics
        total_wells = len(wells)
        total_rows = len(df)
        overall_quality = self._calculate_overall_quality(intervals)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(intervals, total_wells, total_rows)
        
        # Analyze sequence feasibility for common lengths
        sequence_lengths = [64, 32, 16, 12, 8, 6, 4]
        sequence_feasibility = {}
        
        for seq_len in sequence_lengths:
            feasibility = self._analyze_sequence_feasibility(intervals, seq_len)
            sequence_feasibility[seq_len] = feasibility
            
            if self.verbose:
                status = "✅" if feasibility['feasible'] else "❌"
                print(f"   {status} Sequence Length {seq_len}: {feasibility['estimated_sequences']} sequences")
        
        # Create comprehensive report
        report = IntervalAnalysisReport(
            total_wells=total_wells,
            total_rows=total_rows,
            intervals=intervals,
            overall_quality=overall_quality,
            recommendations=recommendations,
            sequence_feasibility=sequence_feasibility,
            analysis_timestamp=datetime.now().isoformat()
        )
        
        if self.verbose:
            self._print_analysis_summary(report)
        
        if self.export_results:
            self._export_analysis_report(report)
        
        self.analysis_history.append(report)
        return report
    
    def _analyze_well_intervals(self, well_df: pd.DataFrame, well_name: str,
                               feature_cols: List[str]) -> List[IntervalInfo]:
        """Analyze intervals for a single well with detailed diagnostics."""
        
        well_data = well_df[feature_cols].values
        intervals = []
        
        if len(well_data) == 0:
            if self.verbose:
                print(f"   ⚠️ No data found for well {well_name}")
            return intervals
        
        # Calculate feature completeness
        feature_completeness = {}
        for i, feature in enumerate(feature_cols):
            valid_count = np.sum(~np.isnan(well_data[:, i]))
            completeness = valid_count / len(well_data) if len(well_data) > 0 else 0
            feature_completeness[feature] = completeness
        
        if self.verbose:
            print(f"   📈 Well Statistics:")
            print(f"      • Total rows: {len(well_data)}")
            print(f"      • Feature completeness:")
            for feature, completeness in feature_completeness.items():
                status = "✅" if completeness > 0.8 else "⚠️" if completeness > 0.5 else "❌"
                print(f"        {status} {feature}: {completeness:.1%}")
        
        # Find continuous valid intervals
        all_valid = ~np.isnan(well_data).any(axis=1)
        
        if not np.any(all_valid):
            if self.verbose:
                print(f"   ❌ No valid (complete) rows found in well {well_name}")
            return intervals
        
        # Identify interval boundaries
        breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]
        
        valid_interval_count = 0
        for i in range(len(breaks) - 1):
            start_idx = breaks[i]
            end_idx = breaks[i + 1]
            
            if all_valid[start_idx]:  # This is a valid interval
                length = end_idx - start_idx
                valid_interval_count += 1
                
                # Calculate data quality for this interval
                interval_data = well_data[start_idx:end_idx]
                data_quality = self._calculate_interval_quality(interval_data)
                
                # Identify missing features (should be none for valid intervals)
                missing_features = []
                for j, feature in enumerate(feature_cols):
                    if np.any(np.isnan(interval_data[:, j])):
                        missing_features.append(feature)
                
                interval_info = IntervalInfo(
                    well_name=well_name,
                    start_idx=start_idx,
                    end_idx=end_idx,
                    length=length,
                    data_quality=data_quality,
                    missing_features=missing_features,
                    feature_completeness=feature_completeness
                )
                
                intervals.append(interval_info)
                
                if self.verbose:
                    quality_status = "🟢" if data_quality > 0.8 else "🟡" if data_quality > 0.6 else "🔴"
                    print(f"   {quality_status} Interval {valid_interval_count}: rows {start_idx}-{end_idx} (length: {length}, quality: {data_quality:.3f})")
        
        if self.verbose and valid_interval_count == 0:
            print(f"   ❌ No continuous valid intervals found in well {well_name}")
        
        return intervals
    
    def _calculate_interval_quality(self, interval_data: np.ndarray) -> float:
        """Calculate data quality score for an interval."""
        
        if len(interval_data) == 0:
            return 0.0
        
        # Factors contributing to quality
        completeness = 1.0  # Should be 1.0 for valid intervals
        
        # Check for extreme values (outliers)
        outlier_factor = 1.0
        for col in range(interval_data.shape[1]):
            col_data = interval_data[:, col]
            if len(col_data) > 0:
                q1, q3 = np.percentile(col_data, [25, 75])
                iqr = q3 - q1
                if iqr > 0:
                    outliers = np.sum((col_data < q1 - 1.5 * iqr) | (col_data > q3 + 1.5 * iqr))
                    outlier_ratio = outliers / len(col_data)
                    outlier_factor = min(outlier_factor, 1.0 - outlier_ratio * 0.5)
        
        # Check for data variability (constant values are lower quality)
        variability_factor = 1.0
        for col in range(interval_data.shape[1]):
            col_data = interval_data[:, col]
            if len(col_data) > 1:
                std_dev = np.std(col_data)
                mean_val = np.mean(col_data)
                if mean_val != 0:
                    cv = std_dev / abs(mean_val)  # Coefficient of variation
                    # Penalize very low or very high variability
                    if cv < 0.01:  # Too constant
                        variability_factor *= 0.8
                    elif cv > 2.0:  # Too variable
                        variability_factor *= 0.9
        
        # Length factor (longer intervals are generally better)
        length_factor = min(len(interval_data) / 32, 1.0)  # Normalize to 32 rows
        
        # Combined quality score
        quality = completeness * outlier_factor * variability_factor * length_factor
        return max(0.0, min(1.0, quality))
    
    def _calculate_overall_quality(self, intervals: List[IntervalInfo]) -> float:
        """Calculate overall data quality across all intervals."""
        
        if not intervals:
            return 0.0
        
        # Weight by interval length
        total_length = sum(interval.length for interval in intervals)
        if total_length == 0:
            return 0.0
        
        weighted_quality = sum(
            interval.data_quality * interval.length for interval in intervals
        ) / total_length
        
        return weighted_quality

    def _analyze_sequence_feasibility(self, intervals: List[IntervalInfo],
                                    sequence_length: int) -> Dict[str, Any]:
        """Analyze feasibility of creating sequences with given length."""

        feasible_intervals = []
        estimated_sequences = 0

        for interval in intervals:
            if interval.length >= sequence_length:
                feasible_intervals.append(interval)
                # Assuming stride of 1
                sequences_from_interval = interval.length - sequence_length + 1
                estimated_sequences += sequences_from_interval

        feasible = estimated_sequences > 0

        return {
            'feasible': feasible,
            'feasible_intervals': len(feasible_intervals),
            'total_intervals': len(intervals),
            'estimated_sequences': estimated_sequences,
            'min_interval_length_needed': sequence_length,
            'feasible_wells': list(set(interval.well_name for interval in feasible_intervals))
        }

    def _generate_recommendations(self, intervals: List[IntervalInfo],
                                total_wells: int, total_rows: int) -> List[str]:
        """Generate actionable recommendations based on analysis."""

        recommendations = []

        if not intervals:
            recommendations.append("❌ CRITICAL: No valid intervals found. Check data quality and preprocessing.")
            recommendations.append("🔧 Suggestion: Review data loading and cleaning procedures.")
            return recommendations

        # Analyze interval distribution
        interval_lengths = [interval.length for interval in intervals]
        max_length = max(interval_lengths)
        avg_length = np.mean(interval_lengths)

        if max_length < 16:
            recommendations.append(f"⚠️ SHORT INTERVALS: Maximum interval length is {max_length}. Consider sequence length ≤ {max_length // 2}.")

        if avg_length < 10:
            recommendations.append(f"📊 LOW DENSITY: Average interval length is {avg_length:.1f}. Consider data aggregation or different sampling strategy.")

        # Analyze data quality
        avg_quality = np.mean([interval.data_quality for interval in intervals])
        if avg_quality < 0.6:
            recommendations.append(f"🔧 QUALITY ISSUE: Average data quality is {avg_quality:.3f}. Review outlier detection and data cleaning.")

        # Analyze coverage
        coverage = len(intervals) / total_wells
        if coverage < 0.8:
            recommendations.append(f"📈 COVERAGE ISSUE: Only {coverage:.1%} of wells have valid intervals. Check data completeness.")

        # Sequence length recommendations
        feasible_lengths = []
        for seq_len in [64, 32, 16, 12, 8, 6, 4]:
            feasibility = self._analyze_sequence_feasibility(intervals, seq_len)
            if feasibility['feasible'] and feasibility['estimated_sequences'] >= 10:
                feasible_lengths.append(seq_len)

        if feasible_lengths:
            recommendations.append(f"✅ FEASIBLE LENGTHS: {feasible_lengths} (in order of preference)")
        else:
            recommendations.append("❌ NO FEASIBLE LENGTHS: Consider data preprocessing or collection of additional data.")

        return recommendations

    def _print_analysis_summary(self, report: IntervalAnalysisReport):
        """Print comprehensive analysis summary."""

        print(f"\n📋 Interval Analysis Summary:")
        print(f"   • Total wells analyzed: {report.total_wells}")
        print(f"   • Total data rows: {report.total_rows}")
        print(f"   • Valid intervals found: {len(report.intervals)}")
        print(f"   • Overall data quality: {report.overall_quality:.3f}")

        if report.intervals:
            lengths = [interval.length for interval in report.intervals]
            print(f"   • Interval length range: {min(lengths)} - {max(lengths)}")
            print(f"   • Average interval length: {np.mean(lengths):.1f}")

        print(f"\n🎯 Sequence Feasibility Analysis:")
        for seq_len, feasibility in report.sequence_feasibility.items():
            status = "✅" if feasibility['feasible'] else "❌"
            print(f"   {status} Length {seq_len}: {feasibility['estimated_sequences']} sequences from {feasibility['feasible_intervals']} intervals")

        print(f"\n💡 Recommendations:")
        for recommendation in report.recommendations:
            print(f"   {recommendation}")

    def _export_analysis_report(self, report: IntervalAnalysisReport):
        """Export analysis report to JSON file."""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"interval_analysis_report_{timestamp}.json"

        try:
            with open(filename, 'w') as f:
                json.dump(report.to_dict(), f, indent=2)
            print(f"\n💾 Analysis report exported to: {filename}")
        except Exception as e:
            print(f"\n⚠️ Failed to export report: {e}")

    def get_optimal_sequence_parameters(self, report: IntervalAnalysisReport) -> Dict[str, Any]:
        """Get optimal sequence parameters based on analysis."""

        # Find the best feasible sequence length
        best_length = None
        best_sequences = 0

        for seq_len in sorted(report.sequence_feasibility.keys(), reverse=True):
            feasibility = report.sequence_feasibility[seq_len]
            if feasibility['feasible'] and feasibility['estimated_sequences'] >= 10:
                if feasibility['estimated_sequences'] > best_sequences:
                    best_length = seq_len
                    best_sequences = feasibility['estimated_sequences']

        # Fallback to smallest feasible length
        if best_length is None:
            for seq_len in sorted(report.sequence_feasibility.keys()):
                feasibility = report.sequence_feasibility[seq_len]
                if feasibility['feasible']:
                    best_length = seq_len
                    best_sequences = feasibility['estimated_sequences']
                    break

        # Calculate optimal stride
        optimal_stride = max(1, best_length // 8) if best_length else 1

        return {
            'optimal_sequence_length': best_length or 4,
            'estimated_sequences': best_sequences,
            'optimal_stride': optimal_stride,
            'data_quality': report.overall_quality,
            'feasible': best_length is not None,
            'recommendations': report.recommendations
        }
