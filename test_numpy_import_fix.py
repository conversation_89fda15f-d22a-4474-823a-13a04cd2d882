#!/usr/bin/env python3
"""
Test script to verify the numpy import fix in config_handler.py.

This script tests that the apply_data_sufficiency_optimizations() function
works correctly after adding the numpy import.
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_numpy_import_fix():
    """Test that the numpy import fix resolves the NameError."""
    print("🧪 Testing Numpy Import Fix in config_handler.py")
    print("=" * 60)
    
    try:
        # Test importing the function
        from config_handler import apply_data_sufficiency_optimizations
        print("✅ Successfully imported apply_data_sufficiency_optimizations")
        
        # Create sample data for testing
        print("\n📊 Creating sample test data...")
        sample_data = create_sample_data()
        
        # Create sample hyperparameters
        sample_hparams = {
            'saits': {
                'sequence_len': 64,
                'batch_size': 32,
                'epochs': 50
            },
            'brits': {
                'sequence_len': 64,
                'batch_size': 32,
                'epochs': 50
            }
        }
        
        print("✅ Sample data and hyperparameters created")
        
        # Test the function
        print("\n🔧 Testing apply_data_sufficiency_optimizations...")
        
        optimized_hparams = apply_data_sufficiency_optimizations(
            sample_hparams, 
            sample_data, 
            ['GR', 'NPHI', 'RHOB'], 
            'DT'
        )
        
        print("✅ Function executed successfully!")
        
        # Verify the results
        print("\n📋 Optimization Results:")
        for model_key in ['saits', 'brits']:
            if model_key in optimized_hparams:
                original = sample_hparams[model_key]
                optimized = optimized_hparams[model_key]
                
                print(f"\n{model_key.upper()}:")
                print(f"   • Sequence length: {original['sequence_len']} → {optimized['sequence_len']}")
                print(f"   • Batch size: {original['batch_size']} → {optimized['batch_size']}")
                print(f"   • Epochs: {original['epochs']} → {optimized['epochs']}")
        
        return True
        
    except NameError as e:
        print(f"❌ NameError still exists: {e}")
        print("   The numpy import fix did not resolve the issue.")
        return False
        
    except ImportError as e:
        print(f"❌ ImportError: {e}")
        print("   There may be other import issues in config_handler.py")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


def create_sample_data():
    """Create sample data for testing the function."""
    np.random.seed(42)
    
    # Create sample wells with varying sizes
    wells_data = []
    
    # Small wells (should trigger optimizations)
    for i in range(3):
        well_name = f'WELL_{i}'
        n_rows = np.random.randint(20, 40)  # Small wells
        
        well_df = pd.DataFrame({
            'WELL': [well_name] * n_rows,
            'MD': np.arange(n_rows) * 0.5,
            'GR': np.random.normal(50, 20, n_rows),
            'NPHI': np.random.normal(0.2, 0.1, n_rows),
            'RHOB': np.random.normal(2.3, 0.3, n_rows),
            'DT': np.random.normal(100, 30, n_rows)
        })
        wells_data.append(well_df)
    
    # Medium wells
    for i in range(3, 6):
        well_name = f'WELL_{i}'
        n_rows = np.random.randint(60, 100)  # Medium wells
        
        well_df = pd.DataFrame({
            'WELL': [well_name] * n_rows,
            'MD': np.arange(n_rows) * 0.5,
            'GR': np.random.normal(50, 20, n_rows),
            'NPHI': np.random.normal(0.2, 0.1, n_rows),
            'RHOB': np.random.normal(2.3, 0.3, n_rows),
            'DT': np.random.normal(100, 30, n_rows)
        })
        wells_data.append(well_df)
    
    # Combine all wells
    combined_df = pd.concat(wells_data, ignore_index=True)
    
    # Add some missing values to make it realistic
    missing_mask = np.random.random(combined_df.shape) < 0.05  # 5% missing
    combined_df = combined_df.mask(missing_mask)
    
    print(f"   • Total rows: {len(combined_df)}")
    print(f"   • Wells: {len(combined_df['WELL'].unique())}")
    print(f"   • Average well size: {len(combined_df) / len(combined_df['WELL'].unique()):.1f}")
    
    return combined_df


def test_specific_numpy_functions():
    """Test the specific numpy functions used in the optimization function."""
    print("\n🔬 Testing Specific Numpy Functions")
    print("=" * 60)
    
    try:
        # Test np.diff
        test_array = np.array([True, True, False, False, True])
        diff_result = np.diff(test_array.astype(int))
        print(f"✅ np.diff() works: {diff_result}")
        
        # Test np.where
        where_result = np.where(diff_result != 0)[0]
        print(f"✅ np.where() works: {where_result}")
        
        # Test np.percentile
        test_data = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        percentile_result = np.percentile(test_data, 75)
        print(f"✅ np.percentile() works: {percentile_result}")
        
        # Test np.median
        median_result = np.median(test_data)
        print(f"✅ np.median() works: {median_result}")
        
        print("\n✅ All numpy functions are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Numpy function test failed: {e}")
        return False


def main():
    """Run all tests to verify the numpy import fix."""
    print("🚀 Numpy Import Fix Verification")
    print("=" * 80)
    
    # Test 1: Specific numpy functions
    numpy_test = test_specific_numpy_functions()
    
    # Test 2: Full function test
    function_test = test_numpy_import_fix()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    tests = [
        ("Numpy Functions", numpy_test),
        ("Data Sufficiency Function", function_test)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SUCCESS! The numpy import fix is working correctly.")
        print("\nNext steps:")
        print("1. Run main.py")
        print("2. Step 8.5 (Data sufficiency optimization) should now complete successfully")
        print("3. The pipeline should proceed to model training without NameError")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")
        print("There may be additional issues beyond the numpy import.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
