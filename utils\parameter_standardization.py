#!/usr/bin/env python3
"""
Parameter Standardization Module for SAITS Model
Phase 1 Implementation: Standardize parameter handling across methods

This module provides standardized parameter handling to resolve parameter 
signature mismatches between enhanced and basic methods.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import warnings


@dataclass
class StandardizedParameters:
    """
    Standardized parameters for sequence creation methods.
    Phase 1 Implementation: Consistent parameter structure.
    """
    # Core sequence parameters
    sequence_length: int = 64
    stride: int = 1
    
    # Data parameters
    feature_cols: List[str] = None
    well_col: str = 'WELL'
    target_col: Optional[str] = None
    
    # Method parameters
    method: str = 'enhanced'
    fallback_enabled: bool = True
    
    # Quality parameters
    min_sequence_length: int = 4
    max_sequence_length: int = 128
    min_sequence_count: int = 10
    
    # Processing parameters
    adaptive_stride: bool = True
    adaptive_length: bool = True
    verbose: bool = True
    
    # Missing data parameters
    missing_rate: float = 0.2
    random_seed: int = 42
    
    # Prediction parameters
    prediction_mode: bool = False
    target_well: Optional[str] = None
    
    def __post_init__(self):
        """Validate parameters after initialization."""
        if self.feature_cols is None:
            self.feature_cols = []
        
        # Validate sequence length bounds
        if self.sequence_length < self.min_sequence_length:
            warnings.warn(f"sequence_length {self.sequence_length} < min_sequence_length {self.min_sequence_length}")
            self.sequence_length = self.min_sequence_length
        
        if self.sequence_length > self.max_sequence_length:
            warnings.warn(f"sequence_length {self.sequence_length} > max_sequence_length {self.max_sequence_length}")
            self.sequence_length = self.max_sequence_length
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for compatibility."""
        return {
            'sequence_length': self.sequence_length,
            'stride': self.stride,
            'feature_cols': self.feature_cols,
            'well_col': self.well_col,
            'target_col': self.target_col,
            'method': self.method,
            'fallback_enabled': self.fallback_enabled,
            'min_sequence_length': self.min_sequence_length,
            'max_sequence_length': self.max_sequence_length,
            'min_sequence_count': self.min_sequence_count,
            'adaptive_stride': self.adaptive_stride,
            'adaptive_length': self.adaptive_length,
            'verbose': self.verbose,
            'missing_rate': self.missing_rate,
            'random_seed': self.random_seed,
            'prediction_mode': self.prediction_mode,
            'target_well': self.target_well
        }
    
    @classmethod
    def from_dict(cls, params: Dict[str, Any]) -> 'StandardizedParameters':
        """Create from dictionary."""
        return cls(**{k: v for k, v in params.items() if k in cls.__annotations__})


class ParameterStandardizer:
    """
    Parameter standardizer for sequence creation methods.
    Phase 1 Implementation: Resolve parameter signature mismatches.
    """
    
    def __init__(self):
        """Initialize parameter standardizer."""
        self.default_params = StandardizedParameters()
        self.method_signatures = {}
        self._register_method_signatures()
    
    def _register_method_signatures(self):
        """Register known method signatures."""
        
        # Enhanced method signature
        self.method_signatures['enhanced'] = {
            'required': ['df', 'well_col', 'feature_cols'],
            'optional': ['sequence_length', 'stride', 'prediction_mode', 'target_well'],
            'returns': ['sequences', 'metadata']
        }
        
        # Basic method signature
        self.method_signatures['basic'] = {
            'required': ['df', 'well_col', 'feature_cols', 'sequence_len', 'step'],
            'optional': ['allow_nan_cols'],
            'returns': ['sequences', 'metadata']
        }
        
        # Adaptive method signature
        self.method_signatures['adaptive'] = {
            'required': ['df', 'well_col', 'feature_cols'],
            'optional': ['sequence_length', 'stride', 'prediction_mode', 'target_well', 'verbose'],
            'returns': ['sequences', 'metadata']
        }
    
    def standardize_parameters(self, method: str, **kwargs) -> StandardizedParameters:
        """
        Standardize parameters for a specific method.
        
        Args:
            method: Method name ('enhanced', 'basic', 'adaptive')
            **kwargs: Input parameters
            
        Returns:
            StandardizedParameters object
        """
        
        # Start with default parameters
        params = StandardizedParameters()
        
        # Update with provided parameters
        for key, value in kwargs.items():
            if hasattr(params, key):
                setattr(params, key, value)
            else:
                # Handle parameter name variations
                standardized_key = self._standardize_parameter_name(key)
                if hasattr(params, standardized_key):
                    setattr(params, standardized_key, value)
        
        # Method-specific adjustments
        if method == 'basic':
            # Basic method uses 'sequence_len' instead of 'sequence_length'
            if 'sequence_len' in kwargs:
                params.sequence_length = kwargs['sequence_len']
            if 'step' in kwargs:
                params.stride = kwargs['step']
        
        # Set method
        params.method = method
        
        return params
    
    def _standardize_parameter_name(self, param_name: str) -> str:
        """Standardize parameter names to resolve naming inconsistencies."""
        
        name_mapping = {
            'sequence_len': 'sequence_length',
            'seq_len': 'sequence_length',
            'seq_length': 'sequence_length',
            'step': 'stride',
            'sequence_step': 'stride',
            'seq_stride': 'stride',
            'features': 'feature_cols',
            'feature_columns': 'feature_cols',
            'well_column': 'well_col',
            'well_id': 'well_col',
            'target_column': 'target_col',
            'target': 'target_col',
            'allow_nan_cols': 'fallback_enabled',
            'enable_fallback': 'fallback_enabled'
        }
        
        return name_mapping.get(param_name, param_name)
    
    def convert_to_method_signature(self, params: StandardizedParameters, method: str) -> Dict[str, Any]:
        """
        Convert standardized parameters to method-specific signature.
        
        Args:
            params: StandardizedParameters object
            method: Target method name
            
        Returns:
            Dictionary with method-specific parameter names
        """
        
        if method not in self.method_signatures:
            raise ValueError(f"Unknown method: {method}")
        
        signature = self.method_signatures[method]
        result = {}
        
        if method == 'enhanced':
            result = {
                'sequence_length': params.sequence_length,
                'stride': params.stride,
                'prediction_mode': params.prediction_mode,
                'target_well': params.target_well
            }
        
        elif method == 'basic':
            result = {
                'sequence_len': params.sequence_length,
                'step': params.stride,
                'allow_nan_cols': None if params.fallback_enabled else []
            }
        
        elif method == 'adaptive':
            result = {
                'sequence_length': params.sequence_length,
                'stride': params.stride,
                'prediction_mode': params.prediction_mode,
                'target_well': params.target_well,
                'verbose': params.verbose
            }
        
        return result
    
    def validate_parameters(self, params: StandardizedParameters, method: str) -> Tuple[bool, List[str]]:
        """
        Validate parameters for a specific method.
        
        Args:
            params: StandardizedParameters object
            method: Method name
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        
        issues = []
        
        # Basic validation
        if not params.feature_cols:
            issues.append("feature_cols cannot be empty")
        
        if params.sequence_length <= 0:
            issues.append("sequence_length must be positive")
        
        if params.stride <= 0:
            issues.append("stride must be positive")
        
        # Method-specific validation
        if method == 'enhanced':
            if params.prediction_mode and not params.target_well:
                issues.append("target_well required when prediction_mode is True")
        
        elif method == 'basic':
            # Basic method has fewer requirements
            pass
        
        elif method == 'adaptive':
            if params.min_sequence_length >= params.max_sequence_length:
                issues.append("min_sequence_length must be < max_sequence_length")
        
        return len(issues) == 0, issues
    
    def create_fallback_parameters(self, original_params: StandardizedParameters, 
                                 data_characteristics: Optional[Dict[str, Any]] = None) -> List[StandardizedParameters]:
        """
        Create fallback parameter sets for progressive fallback.
        
        Args:
            original_params: Original parameters
            data_characteristics: Optional data characteristics for informed fallbacks
            
        Returns:
            List of fallback parameter sets
        """
        
        fallback_sets = []
        
        # Progressive sequence length reduction
        base_length = original_params.sequence_length
        fallback_lengths = []
        
        # Generate fallback lengths
        for factor in [0.75, 0.5, 0.25]:
            fallback_length = max(int(base_length * factor), original_params.min_sequence_length)
            if fallback_length not in fallback_lengths and fallback_length != base_length:
                fallback_lengths.append(fallback_length)
        
        # Add standard small lengths
        for length in [16, 12, 8, 6, 4]:
            if length < base_length and length not in fallback_lengths:
                fallback_lengths.append(length)
        
        # Create parameter sets
        for length in fallback_lengths:
            fallback_params = StandardizedParameters(
                sequence_length=length,
                stride=max(1, length // 8),  # Adaptive stride
                feature_cols=original_params.feature_cols,
                well_col=original_params.well_col,
                target_col=original_params.target_col,
                method=original_params.method,
                fallback_enabled=True,
                verbose=original_params.verbose,
                prediction_mode=original_params.prediction_mode,
                target_well=original_params.target_well
            )
            fallback_sets.append(fallback_params)
        
        return fallback_sets


# Global standardizer instance
_global_standardizer = None

def get_parameter_standardizer() -> ParameterStandardizer:
    """Get global parameter standardizer instance."""
    global _global_standardizer
    if _global_standardizer is None:
        _global_standardizer = ParameterStandardizer()
    return _global_standardizer

def standardize_sequence_parameters(method: str = 'enhanced', **kwargs) -> StandardizedParameters:
    """
    Convenience function to standardize sequence parameters.
    
    Args:
        method: Method name
        **kwargs: Parameters to standardize
        
    Returns:
        StandardizedParameters object
    """
    standardizer = get_parameter_standardizer()
    return standardizer.standardize_parameters(method, **kwargs)
