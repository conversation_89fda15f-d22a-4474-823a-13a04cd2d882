#!/usr/bin/env python3
"""
Test script to verify the enhanced sequence creation fix in the pipeline.
"""

import sys
import os
sys.path.append('.')

def test_enhanced_import():
    """Test that enhanced_preprocessing can be imported and called correctly."""
    print("Testing enhanced_preprocessing import and function call...")
    
    try:
        from preprocessing.deep.enhanced_preprocessing import enhanced_create_sequences
        print("✓ enhanced_create_sequences imported successfully")
        
        # Test function signature
        import inspect
        sig = inspect.signature(enhanced_create_sequences)
        params = list(sig.parameters.keys())
        print(f"✓ Function parameters: {params}")
        print(f"✓ Parameter count: {len(params)}")
        
        if len(params) == 5:
            print("✓ Function signature is correct (5 parameters)")
            return True
        else:
            print(f"✗ Function signature is incorrect (expected 5, got {len(params)})")
            return False
            
    except Exception as e:
        print(f"✗ Import or signature test failed: {e}")
        return False

def test_adaptive_sequence_creator():
    """Test that AdaptiveSequenceCreator calls enhanced functions correctly."""
    print("\nTesting AdaptiveSequenceCreator enhanced method call...")
    
    try:
        from adaptive_sequence_creator import AdaptiveSequenceCreator
        print("✓ AdaptiveSequenceCreator imported successfully")
        
        # Check the _try_enhanced_creation method source
        import inspect
        source = inspect.getsource(AdaptiveSequenceCreator._try_enhanced_creation)
        
        # Check if the problematic parameters are removed
        if 'prediction_mode, target_well' in source:
            print("✗ Function still contains problematic parameters")
            return False
        elif 'prediction_mode' in source or 'target_well' in source:
            print("✗ Function still references problematic parameters")
            return False
        else:
            print("✓ Problematic parameters removed from function call")
            return True
            
    except Exception as e:
        print(f"✗ AdaptiveSequenceCreator test failed: {e}")
        return False

def main():
    print("=== Enhanced Sequence Creation Fix Verification ===")
    
    test1_passed = test_enhanced_import()
    test2_passed = test_adaptive_sequence_creator()
    
    print("\n=== Test Results ===")
    print(f"Enhanced import test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Adaptive creator test: {'PASSED' if test2_passed else 'FAILED'}")
    
    overall_success = test1_passed and test2_passed
    print(f"\nOverall result: {'SUCCESS - Fix is working correctly!' if overall_success else 'FAILURE - Fix needs more work'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)