#!/usr/bin/env python3
"""
Quick test to verify the numpy import fix works.
"""

def test_import():
    """Test that the function can be imported without NameError."""
    try:
        from config_handler import apply_data_sufficiency_optimizations
        print("✅ SUCCESS: apply_data_sufficiency_optimizations imported successfully")
        print("✅ The numpy import fix is working!")
        return True
    except NameError as e:
        print(f"❌ FAILED: NameError still exists: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED: Other error: {e}")
        return False

if __name__ == "__main__":
    success = test_import()
    if success:
        print("\n🎉 The fix is working! You can now run main.py successfully.")
        print("Step 8.5 (Data sufficiency optimization) should complete without errors.")
    else:
        print("\n⚠️ There may be additional issues. Check the error message above.")
