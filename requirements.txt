# ML Log Prediction - Production Requirements
# Updated for production-ready pipeline with GPU acceleration and memory optimization

# ===== CORE DATA SCIENCE LIBRARIES =====
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# ===== MACHINE LEARNING LIBRARIES =====
# Gradient Boosting Models (GPU-Accelerated)
scikit-learn>=1.3.0
xgboost>=2.0.0              # Modern GPU acceleration with device='cuda'
lightgbm>=4.0.0             # High-performance GPU gradient boosting
catboost>=1.2.0             # GPU support with task_type='GPU'

# Statistical Analysis
statsmodels>=0.14.0         # MLR diagnostics, VIF calculations

# ===== DEEP LEARNING LIBRARIES =====
# PyTorch Ecosystem (GPU-Optimized)
torch>=2.0.0                # Latest stable with improved CUDA support
torchvision>=0.15.0         # Computer vision utilities
pypots>=0.5.0               # SAITS, BRITS advanced imputation models

# Medical/Scientific Image Processing
monai>=1.2.0                # Enhanced U-Net and advanced architectures

# ===== WELL LOG DATA HANDLING =====
lasio>=0.30                 # LAS (Log ASCII Standard) file processing

# ===== VISUALIZATION LIBRARIES =====
matplotlib>=3.7.0           # Publication-ready plots
plotly>=5.15.0              # Interactive dashboards and 3D visualization
seaborn>=0.12.0             # Statistical visualization

# ===== PERFORMANCE & OPTIMIZATION =====
# Hyperparameter Optimization
optuna>=3.3.0               # Advanced hyperparameter tuning

# Memory and Performance Monitoring
memory-profiler>=0.61.0     # Memory usage tracking
psutil>=5.9.0               # System resource monitoring
tqdm>=4.65.0                # Progress bars for batch processing

# ===== DATA QUALITY & PREPROCESSING =====
missingno>=0.5.1            # Missing data visualization and analysis

# ===== DEVELOPMENT & CODE QUALITY =====
flake8>=6.0.0               # Code linting and style checking

# ===== GPU ACCELERATION SUPPORT =====
# CUDA-related packages (automatically installed with PyTorch CUDA version)
# For manual CUDA installation, use: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# ===== NOTES =====
# - tkinter: Included with Python standard library (GUI file dialogs)
# - CUDA Toolkit: Required for GPU acceleration (install separately)
# - GPU Memory: Optimized for datasets up to 27,618+ samples with adaptive batch processing
# - Python Version: Requires Python 3.8+ (3.9+ recommended for optimal performance)

# ===== INSTALLATION INSTRUCTIONS =====
# 1. Create virtual environment: python -m venv mwlt
# 2. Activate environment: mwlt\Scripts\activate (Windows) or source mwlt/bin/activate (Linux/Mac)
# 3. Install requirements: pip install -r requirements.txt
# 4. For GPU support: Ensure CUDA Toolkit is installed and compatible with PyTorch version