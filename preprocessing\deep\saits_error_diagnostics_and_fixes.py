#!/usr/bin/env python3
"""
SAITS Model Error Diagnostics and Optimization Script

This script provides comprehensive error diagnostics and fixes for SAITS model issues
including PyTorch compilation, GPU memory management, mixed precision conflicts,
and device handling problems.

Author: ML Log Prediction Team
Version: 1.0
Date: 2024
"""

import os
import sys
import gc
import warnings
import logging
import traceback
from typing import Dict, Any, Optional, Tuple, List
from contextlib import contextmanager

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("[ERROR] PyTorch not available")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("[ERROR] NumPy not available")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

class SAITSErrorDiagnostics:
    """
    Comprehensive SAITS model error diagnostics and fixes.
    """
    
    def __init__(self):
        self.device = None
        self.gpu_available = False
        self.mixed_precision_available = False
        self.pypots_available = False
        self.diagnostic_results = {}
        
    def run_full_diagnostics(self) -> Dict[str, Any]:
        """
        Run comprehensive diagnostics for SAITS model setup.
        
        Returns:
            Dict containing diagnostic results and recommendations
        """
        logger.info("Starting SAITS model diagnostics...")
        
        results = {
            'pytorch_status': self._check_pytorch_installation(),
            'gpu_status': self._check_gpu_setup(),
            'pypots_status': self._check_pypots_installation(),
            'memory_status': self._check_memory_status(),
            'device_status': self._check_device_handling(),
            'compilation_status': self._check_pytorch_compilation(),
            'recommendations': []
        }
        
        # Generate recommendations based on diagnostics
        results['recommendations'] = self._generate_recommendations(results)
        
        self.diagnostic_results = results
        return results
    
    def _check_pytorch_installation(self) -> Dict[str, Any]:
        """
        Check PyTorch installation and configuration.
        """
        logger.info("Checking PyTorch installation...")
        
        if not TORCH_AVAILABLE:
            return {
                'status': 'error',
                'message': 'PyTorch not installed',
                'fix': 'Install PyTorch: pip install torch torchvision torchaudio'
            }
        
        try:
            version = torch.__version__
            cuda_available = torch.cuda.is_available()
            cuda_version = torch.version.cuda if cuda_available else None
            
            # Check for known problematic versions
            problematic_versions = ['1.12.0', '1.12.1']  # Example
            version_warning = version in problematic_versions
            
            return {
                'status': 'success',
                'version': version,
                'cuda_available': cuda_available,
                'cuda_version': cuda_version,
                'version_warning': version_warning,
                'message': f'PyTorch {version} installed successfully'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'PyTorch installation check failed: {str(e)}',
                'exception': str(e)
            }
    
    def _check_gpu_setup(self) -> Dict[str, Any]:
        """
        Check GPU setup and CUDA configuration.
        """
        logger.info("Checking GPU setup...")
        
        if not TORCH_AVAILABLE:
            return {'status': 'error', 'message': 'PyTorch not available'}
        
        try:
            cuda_available = torch.cuda.is_available()
            
            if not cuda_available:
                return {
                    'status': 'warning',
                    'message': 'CUDA not available, will use CPU',
                    'device_count': 0,
                    'recommended_device': 'cpu'
                }
            
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            device_name = torch.cuda.get_device_name(current_device)
            memory_total = torch.cuda.get_device_properties(current_device).total_memory
            memory_allocated = torch.cuda.memory_allocated(current_device)
            memory_cached = torch.cuda.memory_reserved(current_device)
            
            # Check for memory fragmentation
            memory_free = memory_total - memory_cached
            fragmentation_ratio = memory_cached / memory_total if memory_total > 0 else 0
            
            self.gpu_available = True
            
            return {
                'status': 'success',
                'cuda_available': True,
                'device_count': device_count,
                'current_device': current_device,
                'device_name': device_name,
                'memory_total_gb': memory_total / (1024**3),
                'memory_allocated_gb': memory_allocated / (1024**3),
                'memory_cached_gb': memory_cached / (1024**3),
                'memory_free_gb': memory_free / (1024**3),
                'fragmentation_ratio': fragmentation_ratio,
                'recommended_device': f'cuda:{current_device}',
                'message': f'GPU {device_name} available with {memory_free/(1024**3):.1f}GB free'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'GPU setup check failed: {str(e)}',
                'exception': str(e)
            }
    
    def _check_pypots_installation(self) -> Dict[str, Any]:
        """
        Check PyPOTS installation and compatibility.
        """
        logger.info("Checking PyPOTS installation...")
        
        try:
            import pypots
            from pypots.imputation import SAITS
            
            version = pypots.__version__
            self.pypots_available = True
            
            # Test basic SAITS instantiation
            try:
                test_model = SAITS(
                    n_steps=10,
                    n_features=5,
                    n_layers=1,
                    d_model=64,
                    n_heads=1,
                    epochs=1,
                    batch_size=32
                )
                instantiation_test = True
            except Exception as e:
                instantiation_test = False
                instantiation_error = str(e)
            
            return {
                'status': 'success',
                'version': version,
                'saits_available': True,
                'instantiation_test': instantiation_test,
                'instantiation_error': instantiation_error if not instantiation_test else None,
                'message': f'PyPOTS {version} installed successfully'
            }
            
        except ImportError as e:
            return {
                'status': 'error',
                'message': 'PyPOTS not installed',
                'fix': 'Install PyPOTS: pip install pypots',
                'exception': str(e)
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'PyPOTS check failed: {str(e)}',
                'exception': str(e)
            }
    
    def _check_memory_status(self) -> Dict[str, Any]:
        """
        Check system and GPU memory status.
        """
        logger.info("Checking memory status...")
        
        try:
            import psutil
            
            # System memory
            system_memory = psutil.virtual_memory()
            system_total_gb = system_memory.total / (1024**3)
            system_available_gb = system_memory.available / (1024**3)
            system_usage_percent = system_memory.percent
            
            result = {
                'status': 'success',
                'system_memory_total_gb': system_total_gb,
                'system_memory_available_gb': system_available_gb,
                'system_memory_usage_percent': system_usage_percent
            }
            
            # GPU memory (if available)
            if TORCH_AVAILABLE and torch.cuda.is_available():
                gpu_memory_total = torch.cuda.get_device_properties(0).total_memory
                gpu_memory_allocated = torch.cuda.memory_allocated(0)
                gpu_memory_cached = torch.cuda.memory_reserved(0)
                
                result.update({
                    'gpu_memory_total_gb': gpu_memory_total / (1024**3),
                    'gpu_memory_allocated_gb': gpu_memory_allocated / (1024**3),
                    'gpu_memory_cached_gb': gpu_memory_cached / (1024**3),
                    'gpu_memory_free_gb': (gpu_memory_total - gpu_memory_cached) / (1024**3)
                })
            
            return result
            
        except ImportError:
            return {
                'status': 'warning',
                'message': 'psutil not available for memory monitoring',
                'fix': 'Install psutil: pip install psutil'
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Memory status check failed: {str(e)}',
                'exception': str(e)
            }
    
    def _check_device_handling(self) -> Dict[str, Any]:
        """
        Check device handling and compatibility.
        """
        logger.info("Checking device handling...")
        
        if not TORCH_AVAILABLE:
            return {'status': 'error', 'message': 'PyTorch not available'}
        
        try:
            # Test device selection logic
            if torch.cuda.is_available():
                device = torch.device('cuda')
                device_type = 'cuda'
            else:
                device = torch.device('cpu')
                device_type = 'cpu'
            
            # Test tensor creation and movement
            test_tensor = torch.randn(10, 10)
            test_tensor = test_tensor.to(device)
            
            # Test mixed precision availability
            mixed_precision_available = hasattr(torch.cuda.amp, 'autocast')
            self.mixed_precision_available = mixed_precision_available
            
            self.device = device
            
            return {
                'status': 'success',
                'recommended_device': str(device),
                'device_type': device_type,
                'mixed_precision_available': mixed_precision_available,
                'tensor_test_passed': True,
                'message': f'Device handling working correctly with {device_type}'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Device handling check failed: {str(e)}',
                'exception': str(e)
            }
    
    def _check_pytorch_compilation(self) -> Dict[str, Any]:
        """
        Check PyTorch compilation and JIT issues.
        """
        logger.info("Checking PyTorch compilation...")
        
        if not TORCH_AVAILABLE:
            return {'status': 'error', 'message': 'PyTorch not available'}
        
        try:
            # Check JIT compilation
            @torch.jit.script
            def test_jit_function(x):
                return x * 2
            
            test_input = torch.randn(5)
            jit_result = test_jit_function(test_input)
            jit_available = True
            
        except Exception as e:
            jit_available = False
            jit_error = str(e)
        
        try:
            # Check CUDA compilation (if available)
            if torch.cuda.is_available():
                test_tensor = torch.randn(100, 100, device='cuda')
                cuda_result = torch.matmul(test_tensor, test_tensor)
                cuda_compilation_ok = True
            else:
                cuda_compilation_ok = None
                
        except Exception as e:
            cuda_compilation_ok = False
            cuda_error = str(e)
        
        return {
            'status': 'success' if jit_available else 'warning',
            'jit_available': jit_available,
            'jit_error': jit_error if not jit_available else None,
            'cuda_compilation_ok': cuda_compilation_ok,
            'cuda_error': cuda_error if cuda_compilation_ok is False else None,
            'message': 'Compilation checks completed'
        }
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """
        Generate recommendations based on diagnostic results.
        """
        recommendations = []
        
        # PyTorch recommendations
        if results['pytorch_status']['status'] == 'error':
            recommendations.append("Install PyTorch: pip install torch torchvision torchaudio")
        
        # GPU recommendations
        gpu_status = results['gpu_status']
        if gpu_status.get('fragmentation_ratio', 0) > 0.8:
            recommendations.append("High GPU memory fragmentation detected. Consider restarting Python session.")
        
        if gpu_status.get('memory_free_gb', 0) < 2.0:
            recommendations.append("Low GPU memory available. Consider reducing batch size or sequence length.")
        
        # PyPOTS recommendations
        if results['pypots_status']['status'] == 'error':
            recommendations.append("Install PyPOTS: pip install pypots")
        
        if not results['pypots_status'].get('instantiation_test', True):
            recommendations.append("PyPOTS SAITS instantiation failed. Check version compatibility.")
        
        # Memory recommendations
        memory_status = results['memory_status']
        if memory_status.get('system_memory_usage_percent', 0) > 90:
            recommendations.append("High system memory usage. Close unnecessary applications.")
        
        # Compilation recommendations
        compilation_status = results['compilation_status']
        if not compilation_status.get('jit_available', True):
            recommendations.append("JIT compilation issues detected. Consider disabling JIT fusion.")
        
        return recommendations

class SAITSOptimizedConfig:
    """
    Optimized SAITS configuration with error handling and fallback mechanisms.
    """
    
    @staticmethod
    def get_robust_config(n_features: int, sequence_len: int, 
                         device: Optional[str] = None,
                         memory_constrained: bool = False) -> Dict[str, Any]:
        """
        Get robust SAITS configuration with automatic optimization.
        
        Args:
            n_features: Number of features
            sequence_len: Sequence length
            device: Target device ('cuda', 'cpu', or None for auto)
            memory_constrained: Whether to use memory-constrained settings
            
        Returns:
            Optimized configuration dictionary
        """
        
        # Auto-detect device if not specified
        if device is None:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                device = 'cuda'
            else:
                device = 'cpu'
        
        # Base configuration - using correct PyPOTS SAITS parameters
        config = {
            'n_steps': sequence_len,
            'n_features': n_features,
            'n_layers': 2,
            'd_model': 128,
            'n_heads': 8,
            'd_k': None,  # Will be auto-calculated
            'd_v': None,  # Will be auto-calculated
            'd_ffn': None,  # Will be auto-calculated
            'dropout': 0.1,
            'ORT_weight': 1,
            'MIT_weight': 1,
            'batch_size': 32,
            'epochs': 100,
            'patience': 10,
            'device': device,
            'saving_path': None,
            'model_saving_strategy': 'best'
        }
        
        # Memory-constrained optimizations
        if memory_constrained or device == 'cpu':
            config.update({
                'batch_size': 16,
                'n_layers': 1,
                'd_model': 64,
                'n_heads': 4,
                'epochs': 50
            })
        
        # GPU-specific optimizations
        if device == 'cuda' and TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                
                if gpu_memory_gb < 4:  # Low memory GPU
                    config.update({
                        'batch_size': 16,
                        'd_model': 64,
                        'n_heads': 4
                    })
                elif gpu_memory_gb >= 8:  # High memory GPU
                    config.update({
                        'batch_size': 64,
                        'd_model': 256,
                        'n_heads': 16
                    })
            except Exception:
                # Fallback if GPU properties can't be accessed
                pass
        
        # Ensure d_model is divisible by n_heads
        while config['d_model'] % config['n_heads'] != 0:
            config['d_model'] += 1
        
        # Auto-calculate derived dimensions
        config['d_k'] = config['d_model'] // config['n_heads']
        config['d_v'] = config['d_model'] // config['n_heads']
        config['d_ffn'] = config['d_model'] * 4
        
        return config
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate SAITS configuration.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Required parameters for PyPOTS SAITS
        required_params = ['n_steps', 'n_features', 'n_layers', 'd_model', 'n_heads']
        for param in required_params:
            if param not in config:
                errors.append(f"Missing required parameter: {param}")
        
        # Validation checks
        if 'n_features' in config and config['n_features'] <= 0:
            errors.append("n_features must be positive")
        
        if 'n_steps' in config and config['n_steps'] <= 0:
            errors.append("n_steps must be positive")
        
        if 'd_model' in config and 'n_heads' in config:
            if config['d_model'] % config['n_heads'] != 0:
                errors.append("d_model must be divisible by n_heads")
        
        if 'batch_size' in config and config['batch_size'] <= 0:
            errors.append("batch_size must be positive")
        
        if 'epochs' in config and config['epochs'] <= 0:
            errors.append("epochs must be positive")
        
        return len(errors) == 0, errors

class SAITSMemoryManager:
    """
    Memory management utilities for SAITS model training.
    """
    
    def __init__(self, device: str = 'cuda'):
        self.device = device
        self.initial_memory = None
        
    @contextmanager
    def memory_efficient_context(self):
        """
        Context manager for memory-efficient training.
        """
        if self.device == 'cuda' and TORCH_AVAILABLE and torch.cuda.is_available():
            # Clear cache before training
            torch.cuda.empty_cache()
            self.initial_memory = torch.cuda.memory_allocated()
            
        try:
            yield
        finally:
            if self.device == 'cuda' and TORCH_AVAILABLE and torch.cuda.is_available():
                # Clear cache after training
                torch.cuda.empty_cache()
                gc.collect()
    
    def optimize_batch_size(self, model, sample_data: torch.Tensor, 
                          initial_batch_size: int = 32) -> int:
        """
        Automatically optimize batch size based on available memory.
        
        Args:
            model: SAITS model instance
            sample_data: Sample data tensor for testing
            initial_batch_size: Starting batch size
            
        Returns:
            Optimized batch size
        """
        if not TORCH_AVAILABLE:
            return initial_batch_size
        
        batch_size = initial_batch_size
        
        while batch_size > 1:
            try:
                # Test with current batch size
                test_batch = sample_data[:batch_size]
                
                if self.device == 'cuda' and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                # Try forward pass
                with torch.no_grad():
                    _ = model(test_batch)
                
                logger.info(f"Optimal batch size found: {batch_size}")
                return batch_size
                
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    batch_size = batch_size // 2
                    logger.warning(f"OOM detected, reducing batch size to {batch_size}")
                    
                    if self.device == 'cuda' and torch.cuda.is_available():
                        torch.cuda.empty_cache()
                else:
                    raise e
        
        return max(1, batch_size)
    
    def print_memory_status(self):
        """
        Print current memory status.
        """
        if self.device == 'cuda' and TORCH_AVAILABLE and torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / (1024**3)
            cached = torch.cuda.memory_reserved() / (1024**3)
            total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            logger.info(f"GPU Memory - Allocated: {allocated:.2f}GB, "
                       f"Cached: {cached:.2f}GB, Total: {total:.2f}GB")
        else:
            logger.info("CPU mode - GPU memory monitoring not available")

class SAITSErrorHandler:
    """
    Comprehensive error handling for SAITS model operations.
    """
    
    @staticmethod
    def handle_pytorch_compilation_error(func):
        """
        Decorator to handle PyTorch compilation errors.
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                
                if "jit" in error_msg or "fusion" in error_msg:
                    logger.warning("PyTorch JIT/fusion error detected. Disabling optimizations.")
                    
                    # Disable JIT fusion
                    if TORCH_AVAILABLE:
                        torch._C._jit_set_profiling_mode(False)
                        torch._C._jit_set_profiling_executor(False)
                        torch._C._set_graph_executor_optimize(False)
                    
                    # Retry without optimizations
                    return func(*args, **kwargs)
                else:
                    raise e
        return wrapper
    
    @staticmethod
    def handle_gpu_memory_error(func):
        """
        Decorator to handle GPU memory errors.
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    logger.warning("GPU OOM detected. Implementing memory optimizations.")
                    
                    # Clear GPU cache
                    if TORCH_AVAILABLE and torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        gc.collect()
                    
                    # Reduce batch size if possible
                    if 'batch_size' in kwargs:
                        kwargs['batch_size'] = max(1, kwargs['batch_size'] // 2)
                        logger.info(f"Reduced batch size to {kwargs['batch_size']}")
                        return func(*args, **kwargs)
                    
                    # Switch to CPU if necessary
                    if 'device' in kwargs and kwargs['device'] != 'cpu':
                        logger.warning("Switching to CPU due to GPU memory constraints")
                        kwargs['device'] = 'cpu'
                        return func(*args, **kwargs)
                
                raise e
        return wrapper
    
    @staticmethod
    def handle_mixed_precision_error(func):
        """
        Decorator to handle mixed precision training errors.
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                
                if "autocast" in error_msg or "scaler" in error_msg or "amp" in error_msg:
                    logger.warning("Mixed precision error detected. Disabling mixed precision.")
                    
                    # Disable mixed precision if possible
                    if 'use_mixed_precision' in kwargs:
                        kwargs['use_mixed_precision'] = False
                        return func(*args, **kwargs)
                
                raise e
        return wrapper

def create_robust_saits_model(n_features: int, sequence_len: int, 
                             config_override: Optional[Dict[str, Any]] = None,
                             run_diagnostics: bool = True) -> Tuple[Any, Dict[str, Any]]:
    """
    Create a robust SAITS model with comprehensive error handling.
    
    Args:
        n_features: Number of features
        sequence_len: Sequence length
        config_override: Optional configuration overrides
        run_diagnostics: Whether to run diagnostics first
        
    Returns:
        Tuple of (model_instance, configuration_used)
    """
    
    # Run diagnostics if requested
    if run_diagnostics:
        diagnostics = SAITSErrorDiagnostics()
        diag_results = diagnostics.run_full_diagnostics()
        
        logger.info("Diagnostic Results:")
        for key, result in diag_results.items():
            if key != 'recommendations' and isinstance(result, dict):
                status = result.get('status', 'unknown')
                message = result.get('message', 'No message')
                logger.info(f"  {key}: {status} - {message}")
        
        if diag_results['recommendations']:
            logger.warning("Recommendations:")
            for rec in diag_results['recommendations']:
                logger.warning(f"  - {rec}")
    
    # Get optimized configuration
    memory_constrained = False
    if run_diagnostics and 'memory_status' in diag_results:
        memory_status = diag_results['memory_status']
        gpu_memory = memory_status.get('gpu_memory_free_gb', float('inf'))
        system_memory = memory_status.get('system_memory_available_gb', float('inf'))
        memory_constrained = gpu_memory < 4 or system_memory < 8
    
    config = SAITSOptimizedConfig.get_robust_config(
        n_features=n_features,
        sequence_len=sequence_len,
        memory_constrained=memory_constrained
    )
    
    # Apply configuration overrides
    if config_override:
        config.update(config_override)
    
    # Validate configuration
    is_valid, errors = SAITSOptimizedConfig.validate_config(config)
    if not is_valid:
        raise ValueError(f"Invalid SAITS configuration: {errors}")
    
    # Create model with error handling
    try:
        from pypots.imputation import SAITS
        
        # Filter config to only include valid PyPOTS SAITS parameters
        valid_saits_params = {
            'n_steps', 'n_features', 'n_layers', 'd_model', 'n_heads', 
            'd_k', 'd_v', 'd_ffn', 'dropout', 'ORT_weight', 'MIT_weight',
            'batch_size', 'epochs', 'patience', 'device', 'saving_path', 
            'model_saving_strategy'
        }
        
        filtered_config = {k: v for k, v in config.items() if k in valid_saits_params}
        
        # Debug logging to see what's being passed
        logger.info(f"Original config keys: {list(config.keys())}")
        logger.info(f"Filtered config keys: {list(filtered_config.keys())}")
        if 'learning_rate' in config:
            logger.warning(f"Found learning_rate in original config: {config['learning_rate']}")
        
        # Apply error handling decorators
        @SAITSErrorHandler.handle_pytorch_compilation_error
        @SAITSErrorHandler.handle_gpu_memory_error
        @SAITSErrorHandler.handle_mixed_precision_error
        def create_model():
            logger.info(f"About to create SAITS with config: {filtered_config}")
            return SAITS(**filtered_config)
        
        model = create_model()
        logger.info("SAITS model created successfully")
        
        return model, config
        
    except Exception as e:
        logger.error(f"Failed to create SAITS model: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

def optimize_saits_training(model, train_data: np.ndarray, 
                          config: Dict[str, Any]) -> Any:
    """
    Optimize SAITS model training with memory management and error handling.
    
    Args:
        model: SAITS model instance
        train_data: Training data
        config: Model configuration
        
    Returns:
        Trained model
    """
    
    device = config.get('device', 'cpu')
    memory_manager = SAITSMemoryManager(device)
    
    # Apply error handling decorators
    @SAITSErrorHandler.handle_pytorch_compilation_error
    @SAITSErrorHandler.handle_gpu_memory_error
    @SAITSErrorHandler.handle_mixed_precision_error
    def train_model():
        with memory_manager.memory_efficient_context():
            logger.info("Starting SAITS model training...")
            memory_manager.print_memory_status()
            
            # Train the model
            model.fit(train_data)
            
            logger.info("SAITS model training completed")
            memory_manager.print_memory_status()
            
            return model
    
    return train_model()

def main_diagnostic_and_fix_pipeline(n_features: int = 5, sequence_len: int = 100,
                                    sample_data: Optional[np.ndarray] = None) -> Dict[str, Any]:
    """
    Main pipeline for SAITS diagnostics and fixes.
    
    Args:
        n_features: Number of features for testing
        sequence_len: Sequence length for testing
        sample_data: Optional sample data for testing
        
    Returns:
        Dictionary with results and recommendations
    """
    
    logger.info("Starting SAITS diagnostic and fix pipeline...")
    
    try:
        # Step 1: Run comprehensive diagnostics
        diagnostics = SAITSErrorDiagnostics()
        diag_results = diagnostics.run_full_diagnostics()
        
        # Step 2: Create robust model configuration
        model, config = create_robust_saits_model(
            n_features=n_features,
            sequence_len=sequence_len,
            run_diagnostics=False  # Already ran diagnostics
        )
        
        # Step 3: Test with sample data if provided
        if sample_data is not None:
            logger.info("Testing model with sample data...")
            # Format data for PyPOTS SAITS (expects dictionary with 'X' key)
            formatted_data = {'X': sample_data}
            trained_model = optimize_saits_training(model, formatted_data, config)
            test_success = True
        else:
            logger.info("No sample data provided, skipping training test")
            test_success = None
        
        # Step 4: Generate final report
        final_results = {
            'diagnostics': diag_results,
            'model_config': config,
            'model_creation_success': True,
            'training_test_success': test_success,
            'recommendations': diag_results['recommendations'],
            'status': 'success'
        }
        
        logger.info("SAITS diagnostic and fix pipeline completed successfully")
        return final_results
        
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        return {
            'status': 'error',
            'error_message': str(e),
            'traceback': traceback.format_exc()
        }

if __name__ == "__main__":
    # Example usage
    logger.info("Running SAITS Error Diagnostics and Fixes...")
    
    # Generate sample data for testing
    if NUMPY_AVAILABLE:
        sample_data = np.random.randn(100, 50, 5)  # (samples, timesteps, features)
        sample_data[sample_data > 1.5] = np.nan  # Add some missing values
    else:
        sample_data = None
    
    # Run the main pipeline
    results = main_diagnostic_and_fix_pipeline(
        n_features=5,
        sequence_len=50,
        sample_data=sample_data
    )
    
    # Print results
    print("\n" + "="*60)
    print("SAITS DIAGNOSTIC AND FIX RESULTS")
    print("="*60)
    
    if results['status'] == 'success':
        print("✅ Pipeline completed successfully")
        
        if results['recommendations']:
            print("\n📋 Recommendations:")
            for i, rec in enumerate(results['recommendations'], 1):
                print(f"  {i}. {rec}")
        else:
            print("\n✅ No issues detected - system is ready for SAITS training")
            
    else:
        print("❌ Pipeline failed")
        print(f"Error: {results.get('error_message', 'Unknown error')}")
    
    print("\n" + "="*60)