#!/usr/bin/env python3
"""
Adaptive Sequence Creator for SAITS Model

This module provides adaptive sequence creation with progressive fallback mechanisms
to resolve the critical issue where traditional sequence creation fails due to
short valid intervals in well log data.

Key Features:
- Progressive fallback with multiple sequence lengths
- Intelligent stride adjustment based on data characteristics
- Comprehensive error handling and recovery
- Detailed logging and diagnostics
- Backward compatibility with existing code
- Automatic parameter optimization
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Union
import warnings
from dataclasses import dataclass
import logging
from datetime import datetime

# Phase 1: Enhanced import handling with comprehensive error recovery
try:
    from ...utils.sequence_optimization import SequenceOptimizer, create_adaptive_sequence_parameters
    SEQUENCE_OPTIMIZATION_AVAILABLE = True
    # print("SequenceOptimizer loaded successfully")
except ImportError as e:
    # print(f"SequenceOptimizer not available: {e}")
    SEQUENCE_OPTIMIZATION_AVAILABLE = False

try:
    from ...utils.interval_analyzer import IntervalAnalyzer, IntervalAnalysisReport
    INTERVAL_ANALYZER_AVAILABLE = True
    # print("IntervalAnalyzer loaded successfully")
except ImportError as e:
    # print(f"IntervalAnalyzer not available: {e}")
    INTERVAL_ANALYZER_AVAILABLE = False

# Phase 1: Fallback implementations for missing dependencies
if not INTERVAL_ANALYZER_AVAILABLE:
    # print("Using fallback IntervalAnalysisReport implementation")
    pass
    
    @dataclass
    class IntervalAnalysisReport:
        """Fallback implementation of IntervalAnalysisReport."""
        total_wells: int = 0
        total_intervals: int = 0
        max_interval_length: int = 0
        avg_interval_length: float = 0.0
        recommendations: List[str] = None
        
        def __post_init__(self):
            if self.recommendations is None:
                self.recommendations = []
        
        def to_dict(self) -> Dict[str, Any]:
            return {
                'total_wells': self.total_wells,
                'total_intervals': self.total_intervals,
                'max_interval_length': self.max_interval_length,
                'avg_interval_length': self.avg_interval_length,
                'recommendations': self.recommendations
            }


@dataclass
class SequenceCreationResult:
    """Result of adaptive sequence creation."""
    sequences: np.ndarray
    metadata: Dict[str, Any]
    success: bool
    method_used: str
    parameters_used: Dict[str, Any]
    diagnostics: Dict[str, Any]
    warnings: List[str]
    
    def __post_init__(self):
        """Validate the result after initialization."""
        if self.success and len(self.sequences.shape) != 3:
            self.success = False
            self.warnings.append(f"Invalid sequence shape: {self.sequences.shape}, expected 3D array")


class AdaptiveSequenceCreator:
    """Adaptive sequence creator with intelligent fallback mechanisms."""
    
    def __init__(self, verbose: bool = True, enable_diagnostics: bool = True):
        self.verbose = verbose
        self.enable_diagnostics = enable_diagnostics
        self.creation_history = []
        
        # Setup logging
        if self.verbose:
            logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def create_sequences_adaptive(self, df: pd.DataFrame, well_col: str,
                                feature_cols: List[str], sequence_length: int = 64,
                                stride: int = 1, prediction_mode: bool = False,
                                target_well: Optional[str] = None) -> SequenceCreationResult:
        """Create sequences with adaptive fallback mechanisms."""
        
        start_time = datetime.now()
        warnings_list = []
        
        if self.verbose:
            print(f"\n🚀 Starting Adaptive Sequence Creation...")
            print(f"   Requested: length={sequence_length}, stride={stride}")
            print(f"   Mode: {'Prediction' if prediction_mode else 'Training'}")
            if target_well:
                print(f"   Target well: {target_well}")
        
        try:
            # Step 1: Analyze data characteristics with fallback handling
            if self.enable_diagnostics and INTERVAL_ANALYZER_AVAILABLE:
                analyzer = IntervalAnalyzer(verbose=self.verbose)
                analysis_report = analyzer.analyze_intervals(df, well_col, feature_cols)
                optimal_params = analyzer.get_optimal_sequence_parameters(analysis_report)
            elif self.enable_diagnostics:
                # Fallback analysis using basic methods
                print("🔧 Using fallback data analysis (IntervalAnalyzer not available)")
                analysis_report = self._create_fallback_analysis_report(df, well_col, feature_cols)
                optimal_params = self._get_fallback_optimal_params(analysis_report, sequence_length)
            else:
                analysis_report = None
                optimal_params = {'optimal_sequence_length': sequence_length, 'feasible': True}
            
            # Step 2: Determine sequence creation strategy
            strategy = self._determine_creation_strategy(
                sequence_length, optimal_params, analysis_report
            )
            
            if self.verbose:
                print(f"\n📋 Strategy Selected: {strategy['name']}")
                print(f"   Sequence lengths to try: {strategy['sequence_lengths']}")
                print(f"   Stride adjustment: {strategy['stride_adjustment']}")
            
            # Step 3: Attempt sequence creation with progressive fallback
            result = self._create_sequences_with_fallback(
                df, well_col, feature_cols, strategy, prediction_mode, target_well
            )
            
            # Step 4: Post-process and validate results
            if result.success:
                result = self._post_process_sequences(result, strategy)
            
            # Step 5: Add comprehensive diagnostics
            if self.enable_diagnostics and analysis_report:
                result.diagnostics.update({
                    'analysis_report': analysis_report.to_dict(),
                    'optimal_parameters': optimal_params,
                    'strategy_used': strategy
                })
            
            # Step 6: Log final results
            execution_time = (datetime.now() - start_time).total_seconds()
            self._log_creation_results(result, execution_time)
            
            # Store in history
            self.creation_history.append({
                'timestamp': start_time.isoformat(),
                'result': result,
                'execution_time': execution_time
            })
            
            return result
            
        except Exception as e:
            error_msg = f"Adaptive sequence creation failed: {str(e)}"
            self.logger.error(error_msg)
            
            # Return empty result with error information
            return SequenceCreationResult(
                sequences=np.empty((0, sequence_length, len(feature_cols))),
                metadata={'error': error_msg, 'original_request': {
                    'sequence_length': sequence_length, 'stride': stride,
                    'prediction_mode': prediction_mode, 'target_well': target_well
                }},
                success=False,
                method_used='error_fallback',
                parameters_used={},
                diagnostics={'error': str(e)},
                warnings=[error_msg]
            )
    
    def _determine_creation_strategy(self, requested_length: int,
                                   optimal_params: Dict[str, Any],
                                   analysis_report: Optional[IntervalAnalysisReport]) -> Dict[str, Any]:
        """Determine the best sequence creation strategy."""
        
        if not optimal_params.get('feasible', False):
            # Emergency fallback strategy
            return {
                'name': 'emergency_fallback',
                'sequence_lengths': [8, 6, 4],
                'stride_adjustment': 'minimal',
                'max_attempts': 3
            }
        
        optimal_length = optimal_params.get('optimal_sequence_length', requested_length)
        
        if optimal_length >= requested_length:
            # Standard strategy - requested length is feasible
            return {
                'name': 'standard',
                'sequence_lengths': [requested_length],
                'stride_adjustment': 'none',
                'max_attempts': 1
            }
        
        elif optimal_length >= requested_length * 0.5:
            # Adaptive strategy - use optimal length with fallbacks
            fallback_lengths = [optimal_length]
            for length in [32, 24, 16, 12, 8, 6, 4]:
                if length < optimal_length and length not in fallback_lengths:
                    fallback_lengths.append(length)
                if len(fallback_lengths) >= 3:
                    break
            
            return {
                'name': 'adaptive',
                'sequence_lengths': fallback_lengths,
                'stride_adjustment': 'adaptive',
                'max_attempts': len(fallback_lengths)
            }
        
        else:
            # Aggressive fallback strategy - data is very sparse
            return {
                'name': 'aggressive_fallback',
                'sequence_lengths': [optimal_length, 8, 6, 4],
                'stride_adjustment': 'minimal',
                'max_attempts': 4
            }
    
    def _create_sequences_with_fallback(self, df: pd.DataFrame, well_col: str,
                                      feature_cols: List[str], strategy: Dict[str, Any],
                                      prediction_mode: bool, target_well: Optional[str]) -> SequenceCreationResult:
        """Create sequences with progressive fallback."""
        
        last_error = None
        warnings_list = []
        
        for attempt, seq_length in enumerate(strategy['sequence_lengths']):
            try:
                # Calculate adaptive stride
                stride = self._calculate_adaptive_stride(seq_length, strategy['stride_adjustment'])
                
                if self.verbose:
                    print(f"\n🔄 Attempt {attempt + 1}/{strategy['max_attempts']}: length={seq_length}, stride={stride}")
                
                # Try enhanced sequence creation first
                sequences, metadata = self._try_enhanced_creation(
                    df, well_col, feature_cols, seq_length, stride, prediction_mode, target_well
                )
                
                if sequences.shape[0] > 0:
                    if self.verbose:
                        print(f"   ✅ Success! Created {sequences.shape[0]} sequences")
                    
                    return SequenceCreationResult(
                        sequences=sequences,
                        metadata=metadata,
                        success=True,
                        method_used='enhanced_adaptive',
                        parameters_used={'sequence_length': seq_length, 'stride': stride},
                        diagnostics={'attempt': attempt + 1, 'strategy': strategy['name']},
                        warnings=warnings_list
                    )
                
                # Try basic sequence creation as fallback
                sequences, metadata = self._try_basic_creation(
                    df, well_col, feature_cols, seq_length, stride, prediction_mode, target_well
                )
                
                if sequences.shape[0] > 0:
                    if self.verbose:
                        print(f"   ✅ Success with basic method! Created {sequences.shape[0]} sequences")
                    
                    warnings_list.append(f"Enhanced method failed, used basic method for length {seq_length}")
                    
                    return SequenceCreationResult(
                        sequences=sequences,
                        metadata=metadata,
                        success=True,
                        method_used='basic_adaptive',
                        parameters_used={'sequence_length': seq_length, 'stride': stride},
                        diagnostics={'attempt': attempt + 1, 'strategy': strategy['name']},
                        warnings=warnings_list
                    )
                
                if self.verbose:
                    print(f"   ❌ Failed: No sequences created with length {seq_length}")
                
                warnings_list.append(f"No sequences created with length {seq_length}")
                
            except Exception as e:
                last_error = e
                error_msg = f"Error with sequence length {seq_length}: {str(e)}"
                warnings_list.append(error_msg)
                
                if self.verbose:
                    print(f"   ❌ Error: {error_msg}")
                
                continue
        
        # All attempts failed
        error_msg = f"All sequence creation attempts failed. Last error: {last_error}"
        
        return SequenceCreationResult(
            sequences=np.empty((0, strategy['sequence_lengths'][0], len(feature_cols))),
            metadata={'total_attempts': len(strategy['sequence_lengths']), 'strategy': strategy['name']},
            success=False,
            method_used='all_failed',
            parameters_used={},
            diagnostics={'last_error': str(last_error) if last_error else 'Unknown'},
            warnings=warnings_list
        )
    
    def _calculate_adaptive_stride(self, sequence_length: int, adjustment_type: str) -> int:
        """Calculate adaptive stride based on sequence length and strategy."""
        
        if adjustment_type == 'none':
            return 1
        elif adjustment_type == 'minimal':
            return max(1, sequence_length // 16)  # Very small stride
        elif adjustment_type == 'adaptive':
            return max(1, sequence_length // 8)   # Moderate stride
        else:
            return 1
    
    def _try_enhanced_creation(self, df: pd.DataFrame, well_col: str,
                             feature_cols: List[str], sequence_length: int,
                             stride: int, prediction_mode: bool,
                             target_well: Optional[str]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Try enhanced sequence creation method with timeout protection."""

        try:
            if self.verbose:
                print("🔧 Attempting enhanced sequence creation...")

            # Import enhanced preprocessing if available
            from .enhanced_preprocessing import enhanced_create_sequences

            if self.verbose:
                print("✅ Enhanced preprocessing imported successfully")
                print(f"   Parameters: length={sequence_length}, stride={stride}")

            # Call enhanced sequence creation - this might hang, so we need to be careful
            sequences = enhanced_create_sequences(
                df, well_col, feature_cols, sequence_length, stride
            )

            if self.verbose:
                print(f"✅ Enhanced sequence creation completed: {sequences.shape if hasattr(sequences, 'shape') else 'unknown shape'}")

            metadata = {
                'method': 'enhanced',
                'sequence_length': sequence_length,
                'stride': stride,
                'prediction_mode': prediction_mode,
                'target_well': target_well
            }

            return sequences, metadata

        except ImportError as e:
            if self.verbose:
                print(f"⚠️ Enhanced preprocessing not available: {e}")
            # Enhanced preprocessing not available
            return np.empty((0, sequence_length, len(feature_cols))), {'error': 'enhanced_not_available'}
        except Exception as e:
            if self.verbose:
                print(f"❌ Enhanced method failed: {e}")
            # Enhanced method failed
            return np.empty((0, sequence_length, len(feature_cols))), {'error': str(e)}
    
    def _try_basic_creation(self, df: pd.DataFrame, well_col: str,
                          feature_cols: List[str], sequence_length: int,
                          stride: int, prediction_mode: bool,
                          target_well: Optional[str]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Try basic sequence creation method."""
        
        try:
            sequences = self._create_sequences_basic(
                df, well_col, feature_cols, sequence_length, stride,
                prediction_mode, target_well
            )
            
            metadata = {
                'method': 'basic',
                'sequence_length': sequence_length,
                'stride': stride,
                'prediction_mode': prediction_mode,
                'target_well': target_well
            }
            
            return sequences, metadata
            
        except Exception as e:
            return np.empty((0, sequence_length, len(feature_cols))), {'error': str(e)}
    
    def _create_sequences_basic(self, df: pd.DataFrame, well_col: str,
                              feature_cols: List[str], sequence_length: int,
                              stride: int, prediction_mode: bool,
                              target_well: Optional[str]) -> np.ndarray:
        """Basic sequence creation implementation."""
        
        sequences = []
        wells_to_process = [target_well] if target_well else df[well_col].unique()
        
        for well in wells_to_process:
            well_df = df[df[well_col] == well].copy()
            well_data = well_df[feature_cols].values
            
            if len(well_data) == 0:
                continue
            
            # Find valid continuous intervals
            all_valid = ~np.isnan(well_data).any(axis=1)
            
            if not np.any(all_valid):
                continue
            
            # Find interval boundaries
            breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]
            
            for i in range(len(breaks) - 1):
                start_idx = breaks[i]
                end_idx = breaks[i + 1]
                
                if all_valid[start_idx]:  # Valid interval
                    interval_data = well_data[start_idx:end_idx]
                    
                    # Create sequences from this interval
                    for seq_start in range(0, len(interval_data) - sequence_length + 1, stride):
                        seq_end = seq_start + sequence_length
                        sequence = interval_data[seq_start:seq_end]
                        
                        if sequence.shape[0] == sequence_length:
                            sequences.append(sequence)
        
        if sequences:
            return np.array(sequences)
        else:
            return np.empty((0, sequence_length, len(feature_cols)))
    
    def _post_process_sequences(self, result: SequenceCreationResult,
                              strategy: Dict[str, Any]) -> SequenceCreationResult:
        """Post-process sequences for quality and consistency."""
        
        if not result.success or result.sequences.shape[0] == 0:
            return result
        
        # Validate sequence shape
        expected_shape = (result.sequences.shape[0], result.parameters_used['sequence_length'], result.sequences.shape[2])
        if result.sequences.shape != expected_shape:
            result.warnings.append(f"Unexpected sequence shape: {result.sequences.shape}, expected: {expected_shape}")
        
        # Check for any remaining NaN values
        nan_count = np.sum(np.isnan(result.sequences))
        if nan_count > 0:
            result.warnings.append(f"Found {nan_count} NaN values in sequences")
        
        # Add quality metrics
        result.metadata.update({
            'sequence_count': result.sequences.shape[0],
            'sequence_length': result.sequences.shape[1],
            'feature_count': result.sequences.shape[2],
            'nan_count': int(nan_count),
            'data_range': {
                'min': float(np.nanmin(result.sequences)),
                'max': float(np.nanmax(result.sequences)),
                'mean': float(np.nanmean(result.sequences)),
                'std': float(np.nanstd(result.sequences))
            }
        })
        
        return result
    
    def _log_creation_results(self, result: SequenceCreationResult, execution_time: float):
        """Log comprehensive results of sequence creation."""
        
        if self.verbose:
            print(f"\n📊 Sequence Creation Results:")
            print(f"   • Success: {'✅ Yes' if result.success else '❌ No'}")
            print(f"   • Method used: {result.method_used}")
            print(f"   • Execution time: {execution_time:.2f}s")
            
            if result.success:
                print(f"   • Sequences created: {result.sequences.shape[0]}")
                print(f"   • Sequence shape: {result.sequences.shape}")
                print(f"   • Parameters: {result.parameters_used}")
            
            if result.warnings:
                print(f"   • Warnings ({len(result.warnings)}):")
                for warning in result.warnings:
                    print(f"     ⚠️ {warning}")
    
    def get_creation_summary(self) -> Dict[str, Any]:
        """Get summary of all sequence creation attempts."""
        
        if not self.creation_history:
            return {'total_attempts': 0, 'success_rate': 0.0}
        
        successful = sum(1 for entry in self.creation_history if entry['result'].success)
        total = len(self.creation_history)
        
        return {
            'total_attempts': total,
            'successful_attempts': successful,
            'success_rate': successful / total,
            'average_execution_time': np.mean([entry['execution_time'] for entry in self.creation_history]),
            'methods_used': list(set(entry['result'].method_used for entry in self.creation_history))
        }
    
    # Phase 1: Fallback methods for missing dependencies
    def _create_fallback_analysis_report(self, df: pd.DataFrame, well_col: str, 
                                        feature_cols: List[str]) -> IntervalAnalysisReport:
        """Create a basic analysis report when IntervalAnalyzer is not available."""
        print("🔧 Creating fallback analysis report...")
        
        try:
            wells = df[well_col].unique()
            total_wells = len(wells)
            
            # Basic interval detection
            max_interval_length = 0
            total_intervals = 0
            interval_lengths = []
            
            for well in wells:
                well_df = df[df[well_col] == well]
                well_data = well_df[feature_cols].values
                
                if len(well_data) > 0:
                    # Simple continuous interval detection
                    all_valid = ~np.isnan(well_data).any(axis=1)
                    
                    if np.any(all_valid):
                        # Find continuous segments
                        segments = []
                        current_start = None
                        
                        for i, is_valid in enumerate(all_valid):
                            if is_valid and current_start is None:
                                current_start = i
                            elif not is_valid and current_start is not None:
                                segments.append((current_start, i))
                                current_start = None
                        
                        # Handle case where valid data goes to end
                        if current_start is not None:
                            segments.append((current_start, len(all_valid)))
                        
                        for start, end in segments:
                            length = end - start
                            interval_lengths.append(length)
                            max_interval_length = max(max_interval_length, length)
                            total_intervals += 1
            
            avg_interval_length = np.mean(interval_lengths) if interval_lengths else 0
            
            recommendations = []
            if max_interval_length < 16:
                recommendations.append("Short intervals detected - recommend sequence length ≤ 8")
            if total_intervals < total_wells:
                recommendations.append("Some wells have no valid intervals")
            
            return IntervalAnalysisReport(
                total_wells=total_wells,
                total_intervals=total_intervals,
                max_interval_length=max_interval_length,
                avg_interval_length=avg_interval_length,
                recommendations=recommendations
            )
            
        except Exception as e:
            print(f"⚠️ Fallback analysis failed: {e}")
            return IntervalAnalysisReport(
                total_wells=0,
                total_intervals=0,
                max_interval_length=4,
                avg_interval_length=4.0,
                recommendations=["Fallback analysis failed - using minimal parameters"]
            )
    
    def _get_fallback_optimal_params(self, analysis_report: IntervalAnalysisReport, 
                                   requested_length: int) -> Dict[str, Any]:
        """Get optimal parameters using fallback analysis."""
        max_length = analysis_report.max_interval_length
        
        # Conservative approach
        if max_length >= requested_length:
            optimal_length = requested_length
        elif max_length >= requested_length // 2:
            optimal_length = max_length // 2
        else:
            optimal_length = min(8, max_length)
        
        optimal_length = max(4, optimal_length)  # Minimum viable length
        
        return {
            'optimal_sequence_length': optimal_length,
            'feasible': optimal_length >= 4,
            'fallback_used': True,
            'max_available_length': max_length
        }


# Convenience function for backward compatibility
def create_sequences_adaptive(df: pd.DataFrame, well_col: str, feature_cols: List[str],
                            sequence_length: int = 64, stride: int = 1,
                            prediction_mode: bool = False, target_well: Optional[str] = None,
                            verbose: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """Convenience function for adaptive sequence creation."""
    
    creator = AdaptiveSequenceCreator(verbose=verbose)
    result = creator.create_sequences_adaptive(
        df, well_col, feature_cols, sequence_length, stride, prediction_mode, target_well
    )
    
    return result.sequences, result.metadata