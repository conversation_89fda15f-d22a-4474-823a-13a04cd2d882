#!/usr/bin/env python3
"""
Simple Phase 2.1 Validation Test
Tests core functionality without complex dependencies
"""

import numpy as np
import pandas as pd
import sys
import os

def create_test_data():
    """Create simple test data for validation."""
    # Create 3 wells with different characteristics
    wells_data = {}
    
    # Well 1: Short data (should test adaptive fallback)
    wells_data['Well_A'] = pd.DataFrame({
        'WELL': ['Well_A'] * 20,
        'DEPTH': range(20),
        'GR': np.random.rand(20) * 100,
        'NPHI': np.random.rand(20) * 0.5,
        'RHOB': 2.0 + np.random.rand(20) * 0.5,
        'RT': np.random.rand(20) * 100
    })
    
    # Well 2: Medium data
    wells_data['Well_B'] = pd.DataFrame({
        'WELL': ['Well_B'] * 50,
        'DEPTH': range(50),
        'GR': np.random.rand(50) * 100,
        'NPHI': np.random.rand(50) * 0.5,
        'RHOB': 2.0 + np.random.rand(50) * 0.5,
        'RT': np.random.rand(50) * 100
    })
    
    # Well 3: Longer data
    wells_data['Well_C'] = pd.DataFrame({
        'WELL': ['Well_C'] * 100,
        'DEPTH': range(100),
        'GR': np.random.rand(100) * 100,
        'NPHI': np.random.rand(100) * 0.5,
        'RHOB': 2.0 + np.random.rand(100) * 0.5,
        'RT': np.random.rand(100) * 100
    })
    
    return wells_data

def test_basic_sequence_creation():
    """Test basic sequence creation functionality."""
    print("🔍 Testing Basic Sequence Creation...")
    
    try:
        # Test if we can import the enhanced preprocessing
        sys.path.append('.')
        from preprocessing.deep.enhanced_preprocessing import EnhancedLogPreprocessor
        
        wells_data = create_test_data()
        feature_cols = ['GR', 'NPHI', 'RHOB', 'RT']
        
        # Test with basic sequence length (4)
        preprocessor = EnhancedLogPreprocessor(sequence_len=4, sequence_stride=1)
        
        baseline_results = {}
        total_sequences = 0
        successful_wells = 0
        
        for well_name, df in wells_data.items():
            try:
                sequences, metadata = preprocessor.create_sequences_enhanced(
                    df, 'WELL', feature_cols
                )
                
                if sequences.shape[0] > 0:
                    successful_wells += 1
                    total_sequences += sequences.shape[0]
                    baseline_results[well_name] = {
                        'sequences': sequences.shape[0],
                        'length': sequences.shape[1],
                        'success': True
                    }
                    print(f"   ✅ {well_name}: {sequences.shape[0]} sequences, length {sequences.shape[1]}")
                else:
                    baseline_results[well_name] = {'sequences': 0, 'length': 0, 'success': False}
                    print(f"   ❌ {well_name}: No sequences created")
                    
            except Exception as e:
                baseline_results[well_name] = {'sequences': 0, 'length': 0, 'success': False, 'error': str(e)}
                print(f"   ❌ {well_name}: Error - {str(e)}")
        
        print(f"\n📊 Basic Results: {total_sequences} total sequences, {successful_wells}/{len(wells_data)} wells successful")
        return baseline_results, total_sequences, successful_wells
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return {}, 0, 0
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return {}, 0, 0

def test_adaptive_sequence_creation():
    """Test adaptive sequence creation functionality."""
    print("\n🚀 Testing Adaptive Sequence Creation...")
    
    try:
        # Test if we can import the adaptive sequence creator
        from adaptive_sequence_creator import AdaptiveSequenceCreator
        
        wells_data = create_test_data()
        feature_cols = ['GR', 'NPHI', 'RHOB', 'RT']
        
        creator = AdaptiveSequenceCreator(verbose=False, enable_diagnostics=False)
        
        adaptive_results = {}
        total_sequences = 0
        successful_wells = 0
        sequence_lengths = []
        
        for well_name, df in wells_data.items():
            try:
                # Request longer sequences (16) to test adaptive behavior
                result = creator.create_sequences_adaptive(
                    df=df,
                    well_col='WELL',
                    feature_cols=feature_cols,
                    sequence_length=16,
                    stride=1
                )
                
                if result.success and result.sequences.shape[0] > 0:
                    successful_wells += 1
                    total_sequences += result.sequences.shape[0]
                    sequence_lengths.append(result.sequences.shape[1])
                    
                    adaptive_results[well_name] = {
                        'sequences': result.sequences.shape[0],
                        'length': result.sequences.shape[1],
                        'success': True,
                        'method': getattr(result, 'method_used', 'unknown')
                    }
                    print(f"   ✅ {well_name}: {result.sequences.shape[0]} sequences, length {result.sequences.shape[1]}")
                else:
                    adaptive_results[well_name] = {'sequences': 0, 'length': 0, 'success': False}
                    print(f"   ❌ {well_name}: No sequences created")
                    
            except Exception as e:
                adaptive_results[well_name] = {'sequences': 0, 'length': 0, 'success': False, 'error': str(e)}
                print(f"   ❌ {well_name}: Error - {str(e)}")
        
        avg_length = np.mean(sequence_lengths) if sequence_lengths else 0
        print(f"\n📊 Adaptive Results: {total_sequences} total sequences, {successful_wells}/{len(wells_data)} wells successful")
        print(f"   Average sequence length: {avg_length:.1f}")
        
        return adaptive_results, total_sequences, successful_wells, avg_length
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return {}, 0, 0, 0
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return {}, 0, 0, 0

def check_implementation_files():
    """Check if Phase 2.1 implementation files exist."""
    print("📁 Checking Phase 2.1 Implementation Files...")
    
    required_files = [
        'adaptive_sequence_creator.py',
        'adaptive_sequence_optimizer.py', 
        'sequence_optimization.py'
    ]
    
    files_found = 0
    for file in required_files:
        if os.path.exists(file):
            files_found += 1
            print(f"   ✅ {file} - Found")
        else:
            print(f"   ❌ {file} - Missing")
    
    print(f"\n📊 Implementation Status: {files_found}/{len(required_files)} files found")
    return files_found == len(required_files)

def validate_success_criteria(baseline_sequences, baseline_wells, adaptive_sequences, adaptive_wells, avg_length):
    """Validate Phase 2.1 success criteria."""
    print("\n🎯 Validating Phase 2.1 Success Criteria...")
    
    results = {}
    
    # Criterion 1: Average sequence length > 8 steps
    criterion_1 = avg_length > 8
    results['avg_length_gt_8'] = criterion_1
    print(f"   1. Average sequence length > 8 steps: {avg_length:.1f} - {'✅ PASS' if criterion_1 else '❌ FAIL'}")
    
    # Criterion 2: Sequence count increase by 50%+
    if baseline_sequences > 0:
        increase_pct = ((adaptive_sequences - baseline_sequences) / baseline_sequences) * 100
        criterion_2 = increase_pct >= 50
    else:
        criterion_2 = adaptive_sequences > 0
        increase_pct = float('inf') if adaptive_sequences > 0 else 0
    
    results['sequence_increase_50pct'] = criterion_2
    print(f"   2. Sequence count increase by 50%+: {increase_pct:.1f}% - {'✅ PASS' if criterion_2 else '❌ FAIL'}")
    
    # Criterion 3: Successful sequence creation for all wells
    total_wells = 3  # We tested 3 wells
    criterion_3 = adaptive_wells == total_wells
    results['all_wells_successful'] = criterion_3
    print(f"   3. All wells successful: {adaptive_wells}/{total_wells} - {'✅ PASS' if criterion_3 else '❌ FAIL'}")
    
    # Overall assessment
    all_passed = all(results.values())
    results['overall'] = all_passed
    
    print(f"\n🏆 Overall Assessment: {'✅ ALL CRITERIA MET' if all_passed else '❌ SOME CRITERIA NOT MET'}")
    
    return results

def run_simple_validation():
    """Run simple Phase 2.1 validation."""
    print("=" * 50)
    print("🧪 SIMPLE PHASE 2.1 VALIDATION")
    print("=" * 50)
    
    try:
        # Step 1: Check implementation files
        files_ok = check_implementation_files()
        if not files_ok:
            print("\n❌ Missing implementation files. Phase 2.1 not complete.")
            return False
        
        # Step 2: Test basic functionality
        baseline_results, baseline_sequences, baseline_wells = test_basic_sequence_creation()
        
        # Step 3: Test adaptive functionality
        adaptive_results, adaptive_sequences, adaptive_wells, avg_length = test_adaptive_sequence_creation()
        
        # Step 4: Validate criteria
        if adaptive_sequences > 0 or baseline_sequences > 0:
            validation_results = validate_success_criteria(
                baseline_sequences, baseline_wells, 
                adaptive_sequences, adaptive_wells, avg_length
            )
            
            overall_success = validation_results.get('overall', False)
            
            print("\n" + "=" * 50)
            print("📋 VALIDATION SUMMARY")
            print("=" * 50)
            
            if overall_success:
                print("🚀 RESULT: Phase 2.1 appears to be working correctly!")
                print("   Recommendation: Proceed to Phase 2.2")
            else:
                print("⚠️ RESULT: Phase 2.1 needs attention")
                print("   Recommendation: Address issues before Phase 2.2")
            
            return overall_success
        else:
            print("\n❌ No sequences created by either method. Implementation may have issues.")
            return False
            
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_simple_validation()
    sys.exit(0 if success else 1)
