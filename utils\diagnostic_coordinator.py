"""Diagnostic Coordinator Module for SAITS Integration

This module implements Phase 1 critical fixes for SAITS integration issues:
- DiagnosticCoordinator class to resolve contradictory diagnostic reports
- Diagnostic state tracking and validation
- Function signature validation for enhanced preprocessing
- Safe wrapper for enhanced training functions

Addresses critical issues:
- Sequence creation diagnostic conflicts (0.0% vs 18.7% missing rate)
- Enhanced training tuple unpacking error (expected 3, got 1)
- Function signature mismatches in enhanced preprocessing pipeline
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, Any, List, Tuple, Optional, Callable
from dataclasses import dataclass
import warnings
import inspect


@dataclass
class DiagnosticState:
    """Data class to store diagnostic state information."""
    module: str
    timestamp: float
    missing_rate: float
    data_shape: Tuple[int, ...]
    data_type: str
    additional_info: Dict[str, Any]


class DiagnosticCoordinator:
    """Central coordinator for diagnostic modules to resolve contradictory reports.
    
    This class addresses the critical issue where different diagnostic modules
    report conflicting missing rates (0.0% vs 18.7%) by providing centralized
    state tracking and validation.
    """
    
    def __init__(self):
        """Initialize the diagnostic coordinator."""
        self.state_history: List[DiagnosticState] = []
        self.modules: Dict[str, Any] = {}
        self.global_state: Dict[str, Any] = {}
        self._missing_rate_cache: Dict[str, float] = {}
    
    def register_module(self, name: str, module: Any) -> None:
        """Register a diagnostic module."""
        self.modules[name] = module
        print(f"[COORDINATOR] Registered module: {name}")
    
    def track_state(self, module: str, data: Any, missing_rate: float, 
                   additional_info: Optional[Dict[str, Any]] = None) -> None:
        """Track diagnostic state from a module."""
        
        # Determine data characteristics
        if isinstance(data, pd.DataFrame):
            data_shape = data.shape
            data_type = "DataFrame"
        elif isinstance(data, np.ndarray):
            data_shape = data.shape
            data_type = "ndarray"
        else:
            data_shape = (len(data),) if hasattr(data, '__len__') else (1,)
            data_type = type(data).__name__
        
        state = DiagnosticState(
            module=module,
            timestamp=time.time(),
            missing_rate=missing_rate,
            data_shape=data_shape,
            data_type=data_type,
            additional_info=additional_info or {}
        )
        
        self.state_history.append(state)
        self._missing_rate_cache[module] = missing_rate
        
        print(f"[COORDINATOR] {module}: missing_rate={missing_rate:.3f}, shape={data_shape}")
    
    def get_consensus_missing_rate(self, data: Any) -> float:
        """Get consensus missing rate by analyzing the data directly."""
        
        if isinstance(data, pd.DataFrame):
            total_values = data.size
            missing_values = data.isnull().sum().sum()
            consensus_rate = missing_values / total_values if total_values > 0 else 0.0
        elif isinstance(data, np.ndarray):
            total_values = data.size
            missing_values = np.isnan(data).sum()
            consensus_rate = missing_values / total_values if total_values > 0 else 0.0
        else:
            # Fallback: use most recent cached rate
            consensus_rate = list(self._missing_rate_cache.values())[-1] if self._missing_rate_cache else 0.0
        
        print(f"[COORDINATOR] Consensus missing rate: {consensus_rate:.3f}")
        return consensus_rate
    
    def validate_diagnostic_consistency(self, tolerance: float = 0.05) -> bool:
        """Validate that diagnostic reports are consistent within tolerance."""
        
        if len(self._missing_rate_cache) < 2:
            return True
        
        rates = list(self._missing_rate_cache.values())
        max_rate = max(rates)
        min_rate = min(rates)
        
        is_consistent = (max_rate - min_rate) <= tolerance
        
        if not is_consistent:
            print(f"[COORDINATOR] WARNING: Inconsistent missing rates detected!")
            print(f"   Range: {min_rate:.3f} - {max_rate:.3f} (tolerance: {tolerance:.3f})")
            for module, rate in self._missing_rate_cache.items():
                print(f"   {module}: {rate:.3f}")
        
        return is_consistent
    
    def get_diagnostic_summary(self) -> Dict[str, Any]:
        """Get summary of all diagnostic states."""
        
        if not self.state_history:
            return {"status": "no_diagnostics", "modules": 0}
        
        recent_states = self.state_history[-5:]  # Last 5 states
        
        summary = {
            "total_states": len(self.state_history),
            "modules": list(self._missing_rate_cache.keys()),
            "missing_rates": dict(self._missing_rate_cache),
            "recent_states": [
                {
                    "module": state.module,
                    "missing_rate": state.missing_rate,
                    "data_shape": state.data_shape,
                    "data_type": state.data_type
                }
                for state in recent_states
            ],
            "is_consistent": self.validate_diagnostic_consistency()
        }
        
        return summary


class FunctionSignatureValidator:
    """Validator for function signatures to prevent tuple unpacking errors."""
    
    @staticmethod
    def validate_enhanced_preprocessing_signature(func: Callable) -> Dict[str, Any]:
        """Validate enhanced preprocessing function signature."""
        
        try:
            sig = inspect.signature(func)
            params = list(sig.parameters.keys())
            
            # Expected parameters for enhanced preprocessing functions
            expected_params = ['df', 'feature_cols', 'target_col', 'model_config', 'hparams']
            
            validation_result = {
                "function_name": func.__name__,
                "parameters": params,
                "parameter_count": len(params),
                "expected_parameters": expected_params,
                "has_required_params": all(param in params for param in expected_params[:3]),
                "signature_valid": True,
                "issues": []
            }
            
            # Check for common issues
            if len(params) < 3:
                validation_result["issues"].append(f"Too few parameters: {len(params)} < 3")
                validation_result["signature_valid"] = False
            
            if 'df' not in params:
                validation_result["issues"].append("Missing 'df' parameter")
                validation_result["signature_valid"] = False
            
            if 'feature_cols' not in params:
                validation_result["issues"].append("Missing 'feature_cols' parameter")
                validation_result["signature_valid"] = False
            
            return validation_result
            
        except Exception as e:
            return {
                "function_name": getattr(func, '__name__', 'unknown'),
                "signature_valid": False,
                "error": str(e),
                "issues": [f"Signature inspection failed: {e}"]
            }
    
    @staticmethod
    def create_safe_wrapper(func: Callable, expected_return_count: int = 2) -> Callable:
        """Create a safe wrapper that handles tuple unpacking issues."""
        
        def safe_wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                
                # Ensure result is a tuple with expected number of elements
                if not isinstance(result, tuple):
                    if expected_return_count == 1:
                        return (result,)
                    elif expected_return_count == 2:
                        return (result, {})
                    else:
                        return tuple([result] + [None] * (expected_return_count - 1))
                
                # Pad tuple if too short
                if len(result) < expected_return_count:
                    padding = [None] * (expected_return_count - len(result))
                    return result + tuple(padding)
                
                # Truncate tuple if too long
                if len(result) > expected_return_count:
                    return result[:expected_return_count]
                
                return result
                
            except Exception as e:
                print(f"[SAFE_WRAPPER] Error in {func.__name__}: {e}")
                # Return safe default values
                if expected_return_count == 1:
                    return (None,)
                elif expected_return_count == 2:
                    return (None, {"error": str(e)})
                else:
                    return tuple([None] * expected_return_count)
        
        # Preserve function metadata
        safe_wrapper.__name__ = f"safe_{func.__name__}"
        safe_wrapper.__doc__ = f"Safe wrapper for {func.__name__}"
        
        return safe_wrapper


class EnhancedTrainingCoordinator:
    """Coordinator for enhanced training functions to prevent integration issues."""
    
    def __init__(self, diagnostic_coordinator: DiagnosticCoordinator):
        """Initialize with diagnostic coordinator."""
        self.diagnostic_coordinator = diagnostic_coordinator
        self.validator = FunctionSignatureValidator()
        self.safe_functions: Dict[str, Callable] = {}
    
    def register_enhanced_function(self, name: str, func: Callable, 
                                 expected_returns: int = 2) -> Callable:
        """Register and create safe wrapper for enhanced function."""
        
        # Validate function signature
        validation = self.validator.validate_enhanced_preprocessing_signature(func)
        
        if not validation["signature_valid"]:
            print(f"[TRAINING_COORDINATOR] WARNING: {name} has signature issues:")
            for issue in validation["issues"]:
                print(f"   - {issue}")
        
        # Create safe wrapper
        safe_func = self.validator.create_safe_wrapper(func, expected_returns)
        self.safe_functions[name] = safe_func
        
        print(f"[TRAINING_COORDINATOR] Registered safe function: {name}")
        return safe_func
    
    def call_enhanced_function(self, name: str, *args, **kwargs) -> Tuple[Any, ...]:
        """Safely call an enhanced function."""
        
        if name not in self.safe_functions:
            raise ValueError(f"Function {name} not registered")
        
        func = self.safe_functions[name]
        
        try:
            result = func(*args, **kwargs)
            print(f"[TRAINING_COORDINATOR] Successfully called {name}")
            return result
        except Exception as e:
            print(f"[TRAINING_COORDINATOR] Error calling {name}: {e}")
            return (None, {"error": str(e)})


# Global coordinator instance
_global_coordinator = None

def get_diagnostic_coordinator() -> DiagnosticCoordinator:
    """Get global diagnostic coordinator instance."""
    global _global_coordinator
    if _global_coordinator is None:
        _global_coordinator = DiagnosticCoordinator()
    return _global_coordinator

def create_enhanced_training_coordinator() -> EnhancedTrainingCoordinator:
    """Create enhanced training coordinator with global diagnostic coordinator."""
    diagnostic_coord = get_diagnostic_coordinator()
    return EnhancedTrainingCoordinator(diagnostic_coord)
