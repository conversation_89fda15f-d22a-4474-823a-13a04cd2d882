#!/usr/bin/env python3
"""
Pipeline Integration Optimization Module - Phase 3

This module implements the Phase 3 components of the SAITS integration solution plan,
focusing on pipeline integration optimization with unified diagnostic framework
and enhanced error handling.

Components:
- UnifiedDiagnosticManager: Central diagnostic coordination
- validate_diagnostic_consistency: State consistency validation
- PipelineFallbackManager: Hierarchical fallback system
- auto_recover_from_error: Automatic error recovery

Author: SAITS Integration Team
Date: 2024
"""

import time
import traceback
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Callable, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PipelineType(Enum):
    """Enumeration of available pipeline types for fallback hierarchy."""
    ENHANCED = "enhanced_pipeline"
    OPTIMIZED = "optimized_pipeline"
    STANDARD = "standard_pipeline"
    BASIC = "basic_pipeline"

class ErrorType(Enum):
    """Enumeration of error types for automatic recovery."""
    TUPLE_UNPACKING = "tuple_unpacking"
    SEQUENCE_CREATION = "sequence_creation"
    MEMORY_OVERFLOW = "memory_overflow"
    DATA_QUALITY = "data_quality"
    DIAGNOSTIC_CONFLICT = "diagnostic_conflict"
    MODEL_TRAINING = "model_training"
    UNKNOWN = "unknown"

@dataclass
class DiagnosticState:
    """Data class to represent diagnostic state information."""
    module_name: str
    timestamp: float
    missing_rate: float
    data_shape: Tuple[int, ...]
    quality_score: Optional[float] = None
    additional_info: Optional[Dict[str, Any]] = None

@dataclass
class RecoveryResult:
    """Data class to represent error recovery results."""
    success: bool
    strategy_used: str
    error_message: Optional[str] = None
    recovered_data: Optional[Any] = None
    recovery_time: Optional[float] = None

class UnifiedDiagnosticManager:
    """
    Central diagnostic manager that coordinates all diagnostic modules
    for consistent reporting and state management.
    """
    
    def __init__(self):
        """Initialize the unified diagnostic manager."""
        self.modules: Dict[str, Any] = {}
        self.global_state: Dict[str, Any] = {}
        self.state_history: List[DiagnosticState] = []
        self.consistency_threshold: float = 0.01  # 1% tolerance for missing rate differences
        
    def register_module(self, name: str, module: Any) -> None:
        """
        Register a diagnostic module with the manager.
        
        Args:
            name: Unique name for the diagnostic module
            module: Module object with a 'diagnose' method
        """
        if not hasattr(module, 'diagnose'):
            raise ValueError(f"Module {name} must have a 'diagnose' method")
        
        self.modules[name] = module
        logger.info(f"Registered diagnostic module: {name}")
    
    def calculate_missing_rate(self, data: Union[np.ndarray, pd.DataFrame]) -> float:
        """
        Calculate missing rate for given data.
        
        Args:
            data: Input data (numpy array or pandas DataFrame)
            
        Returns:
            Missing rate as a percentage (0-100)
        """
        if isinstance(data, pd.DataFrame):
            total_values = data.size
            missing_values = data.isnull().sum().sum()
        elif isinstance(data, np.ndarray):
            total_values = data.size
            missing_values = np.isnan(data).sum()
        else:
            raise ValueError(f"Unsupported data type: {type(data)}")
        
        if total_values == 0:
            return 0.0
        
        return (missing_values / total_values) * 100.0
    
    def log_state(self, module_name: str, data: Any, additional_info: Optional[Dict[str, Any]] = None) -> None:
        """
        Log diagnostic state for a specific module.
        
        Args:
            module_name: Name of the module logging the state
            data: Data being diagnosed
            additional_info: Optional additional information
        """
        try:
            missing_rate = self.calculate_missing_rate(data)
            data_shape = data.shape if hasattr(data, 'shape') else (len(data),)
            
            state = DiagnosticState(
                module_name=module_name,
                timestamp=time.time(),
                missing_rate=missing_rate,
                data_shape=data_shape,
                additional_info=additional_info or {}
            )
            
            self.state_history.append(state)
            
            # Update global state
            self.global_state[f"{module_name}_missing_rate"] = missing_rate
            self.global_state[f"{module_name}_shape"] = data_shape
            self.global_state[f"{module_name}_timestamp"] = state.timestamp
            
            logger.info(f"[DIAG-{module_name}] Missing rate: {missing_rate:.2f}%, Shape: {data_shape}")
            
        except Exception as e:
            logger.error(f"Failed to log state for {module_name}: {e}")
    
    def run_coordinated_diagnostics(self, data: Any) -> Dict[str, Any]:
        """
        Run diagnostics across all registered modules in a coordinated manner.
        
        Args:
            data: Input data for diagnostics
            
        Returns:
            Dictionary containing results from all diagnostic modules
        """
        results = {}
        
        logger.info("Starting coordinated diagnostics...")
        
        for name, module in self.modules.items():
            try:
                logger.info(f"Running {name} diagnostics...")
                
                # Log state before diagnosis
                self.log_state(f"{name}_input", data)
                
                # Run module diagnosis
                module_result = module.diagnose(data)
                results[name] = module_result
                
                # Update global state with module results
                if isinstance(module_result, dict):
                    for key, value in module_result.items():
                        self.global_state[f"{name}_{key}"] = value
                
                logger.info(f"Completed {name} diagnostics")
                
            except Exception as e:
                logger.error(f"Error in {name} diagnostics: {e}")
                results[name] = {"error": str(e), "traceback": traceback.format_exc()}
        
        # Generate unified report
        unified_report = self.generate_unified_report(results)
        
        logger.info("Coordinated diagnostics completed")
        return unified_report
    
    def generate_unified_report(self, diagnostic_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a unified diagnostic report from all module results.
        
        Args:
            diagnostic_results: Results from all diagnostic modules
            
        Returns:
            Unified diagnostic report
        """
        report = {
            "timestamp": time.time(),
            "modules_run": list(diagnostic_results.keys()),
            "global_state": self.global_state.copy(),
            "module_results": diagnostic_results,
            "consistency_check": None,
            "recommendations": []
        }
        
        # Perform consistency validation
        try:
            consistency_result = validate_diagnostic_consistency(diagnostic_results)
            report["consistency_check"] = {
                "passed": True,
                "details": "All diagnostic modules report consistent state"
            }
        except ValueError as e:
            report["consistency_check"] = {
                "passed": False,
                "details": str(e)
            }
            report["recommendations"].append("Investigate diagnostic inconsistencies")
        
        # Add state history summary
        if self.state_history:
            recent_states = self.state_history[-5:]  # Last 5 states
            report["recent_state_history"] = [
                {
                    "module": state.module_name,
                    "missing_rate": state.missing_rate,
                    "shape": state.data_shape,
                    "timestamp": state.timestamp
                }
                for state in recent_states
            ]
        
        return report
    
    def get_state_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current diagnostic state.
        
        Returns:
            Summary of current state
        """
        if not self.state_history:
            return {"status": "No diagnostic history available"}
        
        latest_states = {}
        for state in reversed(self.state_history):
            if state.module_name not in latest_states:
                latest_states[state.module_name] = state
        
        summary = {
            "total_modules": len(self.modules),
            "states_logged": len(self.state_history),
            "latest_states": {
                name: {
                    "missing_rate": state.missing_rate,
                    "shape": state.data_shape,
                    "timestamp": state.timestamp
                }
                for name, state in latest_states.items()
            }
        }
        
        return summary

def validate_diagnostic_consistency(diagnostic_results: Dict[str, Any], tolerance: float = 0.01) -> bool:
    """
    Validate consistency across diagnostic module results.
    
    Args:
        diagnostic_results: Results from multiple diagnostic modules
        tolerance: Tolerance for numerical differences (default 1%)
        
    Returns:
        True if diagnostics are consistent
        
    Raises:
        ValueError: If inconsistencies are detected
    """
    missing_rates = []
    data_shapes = []
    
    # Extract missing rates and data shapes from results
    for module_name, results in diagnostic_results.items():
        if isinstance(results, dict):
            if 'missing_rate' in results:
                missing_rates.append((module_name, results['missing_rate']))
            if 'data_shape' in results:
                data_shapes.append((module_name, results['data_shape']))
            
            # Also check for error conditions
            if 'error' in results:
                logger.warning(f"Module {module_name} reported error: {results['error']}")
    
    # Check missing rate consistency
    if len(missing_rates) > 1:
        rates = [rate for _, rate in missing_rates]
        min_rate, max_rate = min(rates), max(rates)
        
        if abs(max_rate - min_rate) > tolerance * 100:  # Convert tolerance to percentage
            inconsistent_modules = [f"{name}: {rate:.2f}%" for name, rate in missing_rates]
            raise ValueError(
                f"Inconsistent missing rates detected: {', '.join(inconsistent_modules)}. "
                f"Difference {abs(max_rate - min_rate):.2f}% exceeds tolerance {tolerance * 100:.2f}%"
            )
    
    # Check data shape consistency
    if len(data_shapes) > 1:
        shapes = [shape for _, shape in data_shapes]
        unique_shapes = set(shapes)
        
        if len(unique_shapes) > 1:
            inconsistent_shapes = [f"{name}: {shape}" for name, shape in data_shapes]
            raise ValueError(
                f"Inconsistent data shapes detected: {', '.join(inconsistent_shapes)}"
            )
    
    logger.info("Diagnostic consistency validation passed")
    return True

class PipelineFallbackManager:
    """
    Hierarchical fallback system for pipeline execution with automatic recovery.
    """
    
    def __init__(self):
        """Initialize the pipeline fallback manager."""
        self.fallback_chain = [
            PipelineType.ENHANCED,
            PipelineType.OPTIMIZED,
            PipelineType.STANDARD,
            PipelineType.BASIC
        ]
        self.pipeline_functions: Dict[PipelineType, Callable] = {}
        self.execution_history: List[Dict[str, Any]] = []
        
    def register_pipeline(self, pipeline_type: PipelineType, pipeline_function: Callable) -> None:
        """
        Register a pipeline function for a specific type.
        
        Args:
            pipeline_type: Type of pipeline
            pipeline_function: Function to execute for this pipeline type
        """
        self.pipeline_functions[pipeline_type] = pipeline_function
        logger.info(f"Registered pipeline: {pipeline_type.value}")
    
    def execute_pipeline(self, data: Any, operation: str, pipeline_type: PipelineType, **kwargs) -> Any:
        """
        Execute a specific pipeline type.
        
        Args:
            data: Input data
            operation: Operation to perform
            pipeline_type: Type of pipeline to execute
            **kwargs: Additional arguments for the pipeline function
            
        Returns:
            Result from pipeline execution
        """
        if pipeline_type not in self.pipeline_functions:
            raise ValueError(f"Pipeline {pipeline_type.value} not registered")
        
        start_time = time.time()
        
        try:
            logger.info(f"Executing {pipeline_type.value} for operation: {operation}")
            
            pipeline_func = self.pipeline_functions[pipeline_type]
            result = pipeline_func(data, operation, **kwargs)
            
            execution_time = time.time() - start_time
            
            # Log successful execution
            self.execution_history.append({
                "pipeline_type": pipeline_type.value,
                "operation": operation,
                "success": True,
                "execution_time": execution_time,
                "timestamp": time.time()
            })
            
            logger.info(f"Successfully executed {pipeline_type.value} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # Log failed execution
            self.execution_history.append({
                "pipeline_type": pipeline_type.value,
                "operation": operation,
                "success": False,
                "error": str(e),
                "execution_time": execution_time,
                "timestamp": time.time()
            })
            
            logger.error(f"Pipeline {pipeline_type.value} failed after {execution_time:.2f}s: {e}")
            raise
    
    def execute_with_fallback(self, data: Any, operation: str, **kwargs) -> Any:
        """
        Execute operation with hierarchical fallback through pipeline chain.
        
        Args:
            data: Input data
            operation: Operation to perform
            **kwargs: Additional arguments for pipeline functions
            
        Returns:
            Result from successful pipeline execution
            
        Raises:
            RuntimeError: If all pipeline fallbacks fail
        """
        last_error = None
        
        for pipeline_type in self.fallback_chain:
            if pipeline_type not in self.pipeline_functions:
                logger.warning(f"Pipeline {pipeline_type.value} not registered, skipping")
                continue
                
            try:
                result = self.execute_pipeline(data, operation, pipeline_type, **kwargs)
                logger.info(f"Operation '{operation}' succeeded with {pipeline_type.value}")
                return result
                
            except Exception as e:
                last_error = e
                logger.warning(f"Pipeline {pipeline_type.value} failed: {e}")
                
                # Attempt automatic recovery
                try:
                    recovery_result = auto_recover_from_error(
                        self._classify_error(e), data, kwargs
                    )
                    
                    if recovery_result.success:
                        logger.info(f"Auto-recovery successful using {recovery_result.strategy_used}")
                        # Retry with recovered data/config
                        if recovery_result.recovered_data is not None:
                            data = recovery_result.recovered_data
                        continue
                        
                except Exception as recovery_error:
                    logger.error(f"Auto-recovery failed: {recovery_error}")
                
                continue
        
        # All pipelines failed
        error_msg = f"All pipeline fallbacks failed for operation '{operation}'"
        if last_error:
            error_msg += f". Last error: {last_error}"
            
        logger.error(error_msg)
        raise RuntimeError(error_msg)
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """
        Classify error type for automatic recovery.
        
        Args:
            error: Exception that occurred
            
        Returns:
            Classified error type
        """
        error_str = str(error).lower()
        
        if "not enough values to unpack" in error_str:
            return ErrorType.TUPLE_UNPACKING
        elif "sequence" in error_str and ("create" in error_str or "length" in error_str):
            return ErrorType.SEQUENCE_CREATION
        elif "memory" in error_str or "out of memory" in error_str:
            return ErrorType.MEMORY_OVERFLOW
        elif "quality" in error_str or "missing" in error_str:
            return ErrorType.DATA_QUALITY
        elif "diagnostic" in error_str or "inconsistent" in error_str:
            return ErrorType.DIAGNOSTIC_CONFLICT
        elif "training" in error_str or "model" in error_str:
            return ErrorType.MODEL_TRAINING
        else:
            return ErrorType.UNKNOWN
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """
        Get statistics about pipeline execution history.
        
        Returns:
            Dictionary containing execution statistics
        """
        if not self.execution_history:
            return {"status": "No execution history available"}
        
        total_executions = len(self.execution_history)
        successful_executions = sum(1 for exec in self.execution_history if exec["success"])
        
        pipeline_stats = {}
        for pipeline_type in PipelineType:
            pipeline_executions = [exec for exec in self.execution_history 
                                 if exec["pipeline_type"] == pipeline_type.value]
            if pipeline_executions:
                successful = sum(1 for exec in pipeline_executions if exec["success"])
                pipeline_stats[pipeline_type.value] = {
                    "total": len(pipeline_executions),
                    "successful": successful,
                    "success_rate": successful / len(pipeline_executions) * 100
                }
        
        return {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "overall_success_rate": successful_executions / total_executions * 100,
            "pipeline_stats": pipeline_stats
        }

def auto_recover_from_error(error_type: ErrorType, data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """
    Automatic error recovery based on error type.
    
    Args:
        error_type: Type of error encountered
        data_state: Current data state
        config: Configuration parameters
        
    Returns:
        RecoveryResult indicating success/failure and strategy used
    """
    start_time = time.time()
    
    try:
        logger.info(f"Attempting auto-recovery for error type: {error_type.value}")
        
        if error_type == ErrorType.TUPLE_UNPACKING:
            return _fix_tuple_unpacking_error(config)
            
        elif error_type == ErrorType.SEQUENCE_CREATION:
            return _adjust_sequence_parameters(data_state, config)
            
        elif error_type == ErrorType.MEMORY_OVERFLOW:
            return _reduce_batch_size(config)
            
        elif error_type == ErrorType.DATA_QUALITY:
            return _apply_data_cleaning(data_state, config)
            
        elif error_type == ErrorType.DIAGNOSTIC_CONFLICT:
            return _resolve_diagnostic_conflicts(data_state, config)
            
        elif error_type == ErrorType.MODEL_TRAINING:
            return _fix_model_training_issues(data_state, config)
            
        else:
            return _apply_generic_recovery(data_state, config)
            
    except Exception as e:
        recovery_time = time.time() - start_time
        logger.error(f"Auto-recovery failed: {e}")
        
        return RecoveryResult(
            success=False,
            strategy_used=f"auto_recovery_{error_type.value}",
            error_message=str(e),
            recovery_time=recovery_time
        )

def _fix_tuple_unpacking_error(config: Dict[str, Any]) -> RecoveryResult:
    """Fix tuple unpacking errors by adjusting function call parameters."""
    strategy = "tuple_unpacking_fix"
    
    try:
        # Adjust configuration to handle single return values
        new_config = config.copy()
        new_config["expect_single_return"] = True
        new_config["safe_unpacking"] = True
        
        logger.info("Applied tuple unpacking fix: enabled safe unpacking mode")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _adjust_sequence_parameters(data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """Adjust sequence creation parameters based on data characteristics."""
    strategy = "sequence_parameter_adjustment"
    
    try:
        new_config = config.copy()
        
        # Reduce sequence length if data is insufficient
        if "sequence_length" in config:
            current_length = config["sequence_length"]
            new_length = max(8, current_length // 2)
            new_config["sequence_length"] = new_length
            logger.info(f"Reduced sequence length from {current_length} to {new_length}")
        
        # Adjust stride for better sequence coverage
        if "stride" in config:
            new_config["stride"] = 1
            logger.info("Set stride to 1 for maximum sequence coverage")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _reduce_batch_size(config: Dict[str, Any]) -> RecoveryResult:
    """Reduce batch size to handle memory overflow issues."""
    strategy = "batch_size_reduction"
    
    try:
        new_config = config.copy()
        
        if "batch_size" in config:
            current_batch = config["batch_size"]
            new_batch = max(1, current_batch // 2)
            new_config["batch_size"] = new_batch
            logger.info(f"Reduced batch size from {current_batch} to {new_batch}")
        else:
            new_config["batch_size"] = 16  # Default small batch size
            logger.info("Set default small batch size: 16")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _apply_data_cleaning(data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """Apply data cleaning strategies to improve data quality."""
    strategy = "data_cleaning"
    
    try:
        # Enable aggressive data cleaning options
        new_config = config.copy()
        new_config["enable_outlier_removal"] = True
        new_config["enable_interpolation"] = True
        new_config["quality_threshold"] = 0.3  # Lower threshold
        
        logger.info("Applied data cleaning recovery: enabled outlier removal and interpolation")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.2
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _resolve_diagnostic_conflicts(data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """Resolve diagnostic conflicts by standardizing diagnostic approach."""
    strategy = "diagnostic_conflict_resolution"
    
    try:
        new_config = config.copy()
        new_config["use_unified_diagnostics"] = True
        new_config["diagnostic_tolerance"] = 0.05  # 5% tolerance
        
        logger.info("Applied diagnostic conflict resolution: enabled unified diagnostics")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _fix_model_training_issues(data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """Fix model training issues by adjusting training parameters."""
    strategy = "model_training_fix"
    
    try:
        new_config = config.copy()
        new_config["learning_rate"] = 0.001  # Conservative learning rate
        new_config["max_epochs"] = 50  # Reduced epochs
        new_config["early_stopping"] = True
        
        logger.info("Applied model training fix: conservative parameters with early stopping")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

def _apply_generic_recovery(data_state: Any, config: Dict[str, Any]) -> RecoveryResult:
    """Apply generic recovery strategies for unknown error types."""
    strategy = "generic_recovery"
    
    try:
        new_config = config.copy()
        
        # Apply conservative settings
        new_config["safe_mode"] = True
        new_config["verbose_logging"] = True
        new_config["error_tolerance"] = True
        
        logger.info("Applied generic recovery: enabled safe mode with error tolerance")
        
        return RecoveryResult(
            success=True,
            strategy_used=strategy,
            recovered_data=new_config,
            recovery_time=0.1
        )
        
    except Exception as e:
        return RecoveryResult(
            success=False,
            strategy_used=strategy,
            error_message=str(e)
        )

# Example usage and testing functions
def create_sample_diagnostic_module():
    """Create a sample diagnostic module for testing."""
    class SampleDiagnosticModule:
        def __init__(self, name: str):
            self.name = name
        
        def diagnose(self, data: Any) -> Dict[str, Any]:
            if isinstance(data, (np.ndarray, pd.DataFrame)):
                missing_rate = np.random.uniform(0, 20)  # Simulate missing rate
                return {
                    "missing_rate": missing_rate,
                    "data_shape": data.shape if hasattr(data, 'shape') else (len(data),),
                    "quality_score": np.random.uniform(0.3, 0.9)
                }
            else:
                return {"error": "Unsupported data type"}
    
    return SampleDiagnosticModule

def test_pipeline_integration():
    """Test the pipeline integration optimization components."""
    logger.info("Testing Pipeline Integration Optimization...")
    
    # Test UnifiedDiagnosticManager
    manager = UnifiedDiagnosticManager()
    
    # Register sample modules
    SampleModule = create_sample_diagnostic_module()
    manager.register_module("saits_diagnostics", SampleModule("SAITS"))
    manager.register_module("brits_diagnostics", SampleModule("BRITS"))
    
    # Test with sample data
    sample_data = np.random.rand(1000, 5)
    sample_data[np.random.choice(1000, 100, replace=False)] = np.nan
    
    # Run coordinated diagnostics
    results = manager.run_coordinated_diagnostics(sample_data)
    logger.info(f"Diagnostic results: {results['modules_run']}")
    
    # Test PipelineFallbackManager
    fallback_manager = PipelineFallbackManager()
    
    # Register sample pipeline functions
    def enhanced_pipeline(data, operation, **kwargs):
        if np.random.random() < 0.3:  # 30% failure rate
            raise ValueError("Enhanced pipeline failed")
        return f"Enhanced result for {operation}"
    
    def standard_pipeline(data, operation, **kwargs):
        return f"Standard result for {operation}"
    
    fallback_manager.register_pipeline(PipelineType.ENHANCED, enhanced_pipeline)
    fallback_manager.register_pipeline(PipelineType.STANDARD, standard_pipeline)
    
    # Test fallback execution
    try:
        result = fallback_manager.execute_with_fallback(sample_data, "test_operation")
        logger.info(f"Fallback execution result: {result}")
    except Exception as e:
        logger.error(f"Fallback execution failed: {e}")
    
    logger.info("Pipeline Integration Optimization test completed")

if __name__ == "__main__":
    test_pipeline_integration()