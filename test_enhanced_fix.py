#!/usr/bin/env python3
"""
Test script to verify the enhanced sequence creation fix.
"""

import pandas as pd
import numpy as np
from preprocessing.deep.adaptive_sequence_creator import AdaptiveSequenceCreator

def test_enhanced_creation():
    print("Testing enhanced sequence creation after fix...")
    
    # Create test data
    df = pd.DataFrame({
        'WELL': ['A'] * 100,
        'DEPTH': range(100),
        'GR': np.random.rand(100),
        'NPHI': np.random.rand(100)
    })
    
    # Test enhanced creation
    creator = AdaptiveSequenceCreator()
    try:
        result = creator._try_enhanced_creation(
            df, 'WELL', ['GR', 'NPHI'], 10, 1, False, None
        )
        print(f"Enhanced creation result shape: {result.shape if hasattr(result, 'shape') else 'No shape'}")
        print(f"Enhanced creation successful: {len(result) > 0 if hasattr(result, '__len__') else False}")
        return True
    except Exception as e:
        print(f"Enhanced creation failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_creation()
    print(f"Test result: {'PASSED' if success else 'FAILED'}")