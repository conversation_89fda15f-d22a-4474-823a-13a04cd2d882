#!/usr/bin/env python3
"""
Test script for Phase 2.3 toggle functionality in AdaptiveSequenceOptimizer.

This script verifies that the enable_phase23_augmentation parameter correctly
controls the use of advanced vs basic augmentation strategies.
"""

import pandas as pd
import numpy as np
from adaptive_sequence_optimizer import AdaptiveSequenceOptimizer

def create_test_data():
    """
    Create synthetic well log data for testing.
    """
    np.random.seed(42)
    
    # Create test data with multiple wells
    wells = ['WELL_A', 'WELL_B', 'WELL_C']
    data = []
    
    for well in wells:
        # Create short intervals (8-12 rows per well) to test augmentation
        n_rows = np.random.randint(8, 13)
        
        well_data = {
            'WELL': [well] * n_rows,
            'DEPTH': np.arange(1000, 1000 + n_rows * 0.5, 0.5),
            'GR': np.random.normal(50, 15, n_rows),
            'RHOB': np.random.normal(2.3, 0.2, n_rows),
            'NPHI': np.random.normal(0.15, 0.05, n_rows),
            'CALI': np.random.normal(8.5, 1.0, n_rows),
            'SP': np.random.normal(-20, 10, n_rows)
        }
        
        # Add some missing values
        for col in ['GR', 'RHOB', 'NPHI']:
            missing_indices = np.random.choice(n_rows, size=2, replace=False)
            for idx in missing_indices:
                well_data[col][idx] = np.nan
        
        data.extend([{k: v[i] for k, v in well_data.items()} for i in range(n_rows)])
    
    return pd.DataFrame(data)

def test_phase23_enabled():
    """
    Test optimizer with Phase 2.3 features enabled (default).
    """
    print("\n" + "="*60)
    print("TESTING: Phase 2.3 ENABLED (Advanced Augmentation)")
    print("="*60)
    
    # Create optimizer with Phase 2.3 enabled
    optimizer = AdaptiveSequenceOptimizer(
        enable_phase23_augmentation=True,
        verbose=True
    )
    
    # Test data
    test_data = create_test_data()
    feature_cols = ['GR', 'RHOB', 'NPHI', 'CALI', 'SP']
    
    # Run optimization
    result = optimizer.create_optimal_sequences(test_data, 'WELL', feature_cols)
    
    print(f"\nRESULTS (Phase 2.3 ENABLED):")
    print(f"- Success: {result.success}")
    print(f"- Optimal Length: {result.optimal_length}")
    print(f"- Sequences Created: {result.sequences_created}")
    print(f"- Method Used: {result.method_used}")
    print(f"- Fallback Applied: {result.fallback_applied}")
    print(f"- Data Utilization: {result.data_utilization:.2%}")
    print(f"- Recommendations: {result.recommendations}")
    
    return result

def test_phase23_disabled():
    """
    Test optimizer with Phase 2.3 features disabled (basic mode).
    """
    print("\n" + "="*60)
    print("TESTING: Phase 2.3 DISABLED (Basic Augmentation Only)")
    print("="*60)
    
    # Create optimizer with Phase 2.3 disabled
    optimizer = AdaptiveSequenceOptimizer(
        enable_phase23_augmentation=False,
        verbose=True
    )
    
    # Test data
    test_data = create_test_data()
    feature_cols = ['GR', 'RHOB', 'NPHI', 'CALI', 'SP']
    
    # Run optimization
    result = optimizer.create_optimal_sequences(test_data, 'WELL', feature_cols)
    
    print(f"\nRESULTS (Phase 2.3 DISABLED):")
    print(f"- Success: {result.success}")
    print(f"- Optimal Length: {result.optimal_length}")
    print(f"- Sequences Created: {result.sequences_created}")
    print(f"- Method Used: {result.method_used}")
    print(f"- Fallback Applied: {result.fallback_applied}")
    print(f"- Data Utilization: {result.data_utilization:.2%}")
    print(f"- Recommendations: {result.recommendations}")
    
    return result

def compare_results(enabled_result, disabled_result):
    """
    Compare results from both modes.
    """
    print("\n" + "="*60)
    print("COMPARISON ANALYSIS")
    print("="*60)
    
    print(f"Phase 2.3 ENABLED:")
    print(f"  - Method: {enabled_result.method_used}")
    print(f"  - Sequences: {enabled_result.sequences_created}")
    print(f"  - Success: {enabled_result.success}")
    
    print(f"\nPhase 2.3 DISABLED:")
    print(f"  - Method: {disabled_result.method_used}")
    print(f"  - Sequences: {disabled_result.sequences_created}")
    print(f"  - Success: {disabled_result.success}")
    
    # Verify different methods are used
    enabled_method = enabled_result.method_used
    disabled_method = disabled_result.method_used
    
    print(f"\nTOGGLE VERIFICATION:")
    if enabled_result.sequences_created > disabled_result.sequences_created:
        print("✅ SUCCESS: Toggle is working correctly!")
        print("   - Phase 2.3 enabled created more sequences (advanced augmentation)")
        print("   - Phase 2.3 disabled created fewer sequences (basic augmentation)")
    elif enabled_method != disabled_method:
        print("✅ SUCCESS: Toggle is working correctly!")
        print("   - Different methods used for enabled vs disabled")
        print(f"   - Enabled method: {enabled_method}")
        print(f"   - Disabled method: {disabled_method}")
    else:
        print("⚠️ INFO: Both modes used same fallback method")
        print(f"   - Both used method: {enabled_method}")
        print("   - This is expected when data is insufficient for any augmentation")

def main():
    """
    Main test function.
    """
    print("Phase 2.3 Toggle Functionality Test")
    print("This test verifies the enable_phase23_augmentation parameter works correctly")
    
    try:
        # Test both modes
        enabled_result = test_phase23_enabled()
        disabled_result = test_phase23_disabled()
        
        # Compare results
        compare_results(enabled_result, disabled_result)
        
        print("\n" + "="*60)
        print("TEST COMPLETED SUCCESSFULLY")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()