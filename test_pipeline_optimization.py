#!/usr/bin/env python3
"""
Test script for the new pipeline optimization features.

This script tests the new SAITS/BRITS pipeline selection and GPU optimization features
without requiring a full dataset.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_functions():
    """Test the new configuration functions."""
    print("🧪 Testing Pipeline Configuration Functions")
    print("=" * 60)
    
    try:
        from config_handler import configure_gpu_optimization, apply_gpu_optimizations_to_hparams
        
        # Test GPU configuration
        print("\n1. Testing GPU optimization configuration...")
        gpu_config = configure_gpu_optimization()
        print(f"   ✅ GPU config: {gpu_config}")
        
        # Test hyperparameter optimization
        print("\n2. Testing hyperparameter optimization...")
        sample_hparams = {
            'saits': {
                'epochs': 30,
                'batch_size': 128,
                'learning_rate': 1e-3,
                'use_mixed_precision': True
            },
            'brits': {
                'epochs': 30,
                'batch_size': 128,
                'learning_rate': 1e-3,
                'use_mixed_precision': True
            }
        }
        
        optimized_hparams = apply_gpu_optimizations_to_hparams(sample_hparams, gpu_config)
        print(f"   ✅ Optimized hyperparameters applied")
        
        # Show the differences
        for model in ['saits', 'brits']:
            if model in optimized_hparams:
                original = sample_hparams[model]
                optimized = optimized_hparams[model]
                print(f"   {model.upper()}:")
                print(f"     Mixed precision: {original['use_mixed_precision']} → {optimized['use_mixed_precision']}")
                print(f"     Batch size: {original['batch_size']} → {optimized['batch_size']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False


def test_optimization_imports():
    """Test that the optimized functions can be imported."""
    print("\n🧪 Testing Optimization Function Imports")
    print("=" * 60)
    
    try:
        from preprocessing.ml_core_phase1_integration import (
            impute_logs_deep_phase1_safe,
            impute_logs_deep_phase1_optimized,
            configure_optimization_for_hardware,
            OPTIMIZATION_CONFIGS
        )
        
        print("   ✅ All optimization functions imported successfully")
        
        # Test hardware configuration
        print("\n   Testing hardware-aware optimization configuration...")
        hardware_config = configure_optimization_for_hardware()
        print(f"   ✅ Hardware config: {hardware_config}")
        
        # Show available optimization levels
        print("\n   Available optimization levels:")
        for level, config in OPTIMIZATION_CONFIGS.items():
            print(f"     {level}: {config}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import test failed: {e}")
        return False


def test_gpu_detection():
    """Test GPU detection and capability assessment."""
    print("\n🧪 Testing GPU Detection and Capability Assessment")
    print("=" * 60)
    
    try:
        import torch
        
        if torch.cuda.is_available():
            capability = torch.cuda.get_device_capability()
            gpu_name = torch.cuda.get_device_name()
            
            print(f"   ✅ GPU detected: {gpu_name}")
            print(f"   ✅ Compute capability: {capability[0]}.{capability[1]}")
            
            # Test optimization strategy
            if capability[0] >= 7:
                strategy = "modern_gpu"
                mixed_precision = "✅ Supported"
            elif capability[0] == 6:
                strategy = "pascal_gpu"
                mixed_precision = "❌ Disabled (better FP32 performance)"
            else:
                strategy = "cpu"
                mixed_precision = "❌ Not supported"
            
            print(f"   Optimization strategy: {strategy}")
            print(f"   Mixed precision: {mixed_precision}")
            
        else:
            print("   💻 No CUDA GPU detected - CPU optimization will be used")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GPU detection test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Pipeline Optimization Test Suite")
    print("=" * 80)
    
    tests = [
        ("Configuration Functions", test_config_functions),
        ("Optimization Imports", test_optimization_imports),
        ("GPU Detection", test_gpu_detection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The pipeline optimization is ready to use.")
        print("\nNext steps:")
        print("1. Run main.py to see the new pipeline selection interface")
        print("2. Choose option 1 (Optimized Phase 1 Pipeline) for 3-4x speedup")
        print("3. The system will automatically optimize for your GPU hardware")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
