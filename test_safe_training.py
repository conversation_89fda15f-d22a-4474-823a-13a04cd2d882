#!/usr/bin/env python3
"""
Test script for safe_enhanced_training function
"""

import sys
import pandas as pd
from diagnostic_coordinator import safe_enhanced_training

def mock_enhanced_function(df):
    """Mock enhanced training function that returns proper 2-tuple"""
    return df, {'status': 'success', 'model': 'mock'}

def mock_problematic_function(df):
    """Mock function that returns single value (causes tuple unpacking error)"""
    return df

def mock_fallback_function(df):
    """Mock fallback function"""
    return df, {'status': 'fallback', 'model': 'simple'}

def test_safe_enhanced_training():
    """Test the safe_enhanced_training function"""
    print("Testing safe_enhanced_training function...")
    
    # Create test data
    test_df = pd.DataFrame({
        'A': [1, 2, 3, 4, 5],
        'B': [10, 20, 30, 40, 50]
    })
    
    print("\n1. Testing with proper 2-tuple return:")
    result1 = safe_enhanced_training(mock_enhanced_function, test_df)
    print(f"Result: {type(result1)}, Length: {len(result1)}")
    print(f"Data shape: {result1[0].shape}, Model results: {result1[1]}")
    
    print("\n2. Testing with single value return (should add empty model_results):")
    result2 = safe_enhanced_training(mock_problematic_function, test_df)
    print(f"Result: {type(result2)}, Length: {len(result2)}")
    print(f"Data shape: {result2[0].shape}, Model results: {result2[1]}")
    
    print("\n3. Testing with fallback function:")
    result3 = safe_enhanced_training(
        mock_problematic_function, 
        test_df, 
        fallback_func=mock_fallback_function
    )
    print(f"Result: {type(result3)}, Length: {len(result3)}")
    print(f"Data shape: {result3[0].shape}, Model results: {result3[1]}")
    
    print("\nAll tests completed successfully!")
    return True

if __name__ == "__main__":
    test_safe_enhanced_training()