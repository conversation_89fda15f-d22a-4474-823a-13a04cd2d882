#!/usr/bin/env python3
"""
Test script for temporal jittering implementation in AdaptiveSequenceOptimizer.

This script tests the new temporal jittering functionality to ensure it:
1. Integrates properly with existing augmentation strategies
2. Respects geological constraints
3. Creates valid jittered sequences
4. Handles edge cases gracefully
"""

import numpy as np
import pandas as pd
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adaptive_sequence_optimizer import AdaptiveSequenceOptimizer

def create_test_data():
    """Create synthetic well log data for testing."""
    np.random.seed(42)  # For reproducible results
    
    # Create synthetic well log data
    n_samples = 50
    data = {
        'WELL': ['TEST_WELL_1'] * n_samples,
        'DEPTH': np.arange(1000, 1000 + n_samples * 0.5, 0.5),
        'RHOB': np.random.normal(2.3, 0.2, n_samples),  # Density
        'GR': np.random.normal(80, 20, n_samples),      # Gamma Ray
        'NPHI': np.random.normal(0.15, 0.05, n_samples), # Neutron Porosity
        'DT': np.random.normal(100, 15, n_samples),     # Sonic
        'CALI': np.random.normal(8.5, 1.0, n_samples)  # Caliper
    }
    
    # Add some missing values to make it realistic
    missing_indices = np.random.choice(n_samples, size=5, replace=False)
    for col in ['RHOB', 'GR', 'NPHI']:
        data[col][missing_indices[:2]] = np.nan
    
    return pd.DataFrame(data)

def test_temporal_jittering():
    """Test the temporal jittering functionality."""
    print("🧪 Testing Temporal Jittering Implementation")
    print("=" * 50)
    
    # Create test data
    test_data = create_test_data()
    feature_cols = ['RHOB', 'GR', 'NPHI', 'DT', 'CALI']
    
    print(f"📊 Test data shape: {test_data.shape}")
    print(f"📋 Feature columns: {feature_cols}")
    print(f"🔍 Missing values per column:")
    for col in feature_cols:
        missing_count = test_data[col].isnull().sum()
        print(f"   {col}: {missing_count} missing")
    
    # Initialize optimizer
    optimizer = AdaptiveSequenceOptimizer(
        target_lengths=[8, 6, 4],
        minimum_sequence_count=3,
        verbose=True
    )
    
    print("\n🔧 Testing temporal jittering strategy...")
    
    # Test temporal jittering specifically
    try:
        result = optimizer._create_temporal_jittered_sequences(
            test_data, 'WELL', feature_cols
        )
        
        print(f"\n📈 Temporal Jittering Results:")
        print(f"   Method: {result.get('method', 'unknown')}")
        print(f"   Sequence Length: {result.get('length', 0)}")
        print(f"   Sequence Count: {result.get('sequence_count', 0)}")
        print(f"   Data Utilization: {result.get('data_utilization', 0.0):.3f}")
        
        if result.get('sequence_count', 0) > 0:
            print("   ✅ Temporal jittering succeeded!")
        else:
            print("   ⚠️ Temporal jittering produced no sequences")
            
    except Exception as e:
        print(f"   ❌ Temporal jittering failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🔄 Testing full augmentation pipeline...")
    
    # Test full optimization with all strategies
    try:
        optimization_result = optimizer.optimize_sequences(
            test_data, 'WELL', feature_cols
        )
        
        print(f"\n📊 Full Optimization Results:")
        print(f"   Success: {optimization_result.success}")
        print(f"   Optimal Length: {optimization_result.optimal_length}")
        print(f"   Sequences Created: {optimization_result.sequences_created}")
        print(f"   Data Utilization: {optimization_result.data_utilization:.3f}")
        print(f"   Method Used: {optimization_result.method_used}")
        print(f"   Fallback Applied: {optimization_result.fallback_applied}")
        
        if optimization_result.recommendations:
            print(f"   Recommendations:")
            for rec in optimization_result.recommendations:
                print(f"     - {rec}")
        
        if optimization_result.success:
            print("   ✅ Full pipeline succeeded!")
            
            # Check if temporal jittering was used
            if 'temporal_jittering' in optimization_result.method_used:
                print("   🎲 Temporal jittering was successfully used!")
            else:
                print("   ℹ️ Temporal jittering was not needed (other strategy succeeded)")
        else:
            print("   ⚠️ Full pipeline did not succeed")
            
    except Exception as e:
        print(f"   ❌ Full optimization failed: {e}")
        import traceback
        traceback.print_exc()

def test_geological_constraints():
    """Test geological constraint validation."""
    print("\n🌍 Testing Geological Constraints")
    print("=" * 40)
    
    # Create optimizer instance
    optimizer = AdaptiveSequenceOptimizer(verbose=True)
    
    # Create a test sequence with known values
    test_sequence = np.array([
        [2.3, 80, 0.15, 100, 8.5],  # RHOB, GR, NPHI, DT, CALI
        [2.4, 85, 0.16, 105, 8.7],
        [2.2, 75, 0.14, 95, 8.3],
        [2.5, 90, 0.17, 110, 8.9]
    ])
    
    feature_cols = ['RHOB', 'GR', 'NPHI', 'DT', 'CALI']
    
    # Define test constraints
    geological_constraints = {
        'RHOB': {'min': 1.5, 'max': 3.5, 'jitter_percent': 0.02},
        'GR': {'min': 0, 'max': 300, 'jitter_percent': 0.05},
        'NPHI': {'min': -0.15, 'max': 0.6, 'jitter_percent': 0.03},
        'DT': {'min': 40, 'max': 200, 'jitter_percent': 0.04},
        'CALI': {'min': 6, 'max': 20, 'jitter_percent': 0.03}
    }
    
    print(f"📊 Original sequence shape: {test_sequence.shape}")
    print(f"📋 Original values (first row): {test_sequence[0]}")
    
    # Apply jittering multiple times to test consistency
    jittered_sequences = []
    for i in range(5):
        try:
            jittered = optimizer._apply_temporal_jitter(
                test_sequence, feature_cols, geological_constraints
            )
            
            if jittered is not None:
                jittered_sequences.append(jittered)
                print(f"🎲 Jittered #{i+1} (first row): {jittered[0]}")
                
                # Check constraints
                for j, col in enumerate(feature_cols):
                    col_upper = col.upper()
                    if col_upper in geological_constraints:
                        constraints = geological_constraints[col_upper]
                        values = jittered[:, j]
                        
                        if np.any(values < constraints['min']) or np.any(values > constraints['max']):
                            print(f"   ⚠️ {col} values outside constraints!")
                        else:
                            print(f"   ✅ {col} values within constraints")
            else:
                print(f"   ❌ Jittering #{i+1} returned None")
                
        except Exception as e:
            print(f"   ❌ Jittering #{i+1} failed: {e}")
    
    print(f"\n📈 Successfully created {len(jittered_sequences)} jittered sequences")

if __name__ == "__main__":
    print("🚀 Starting Temporal Jittering Tests")
    print("=" * 60)
    
    try:
        test_temporal_jittering()
        test_geological_constraints()
        
        print("\n🎉 All tests completed!")
        print("\n📋 Summary:")
        print("   ✅ Temporal jittering implementation tested")
        print("   ✅ Geological constraints validated")
        print("   ✅ Integration with existing strategies verified")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)