#!/usr/bin/env python3
"""
Adaptive Sequence Optimizer for SAITS Model
Phase 2 Implementation: Sequence Optimization Strategies

This module implements adaptive sequence length selection based on the solution strategy
and best practices from Darts and sktime libraries for time series sequence optimization.

Key Features:
- Progressive sequence length selection from 16->12->8->6->4
- Data-driven optimization based on actual interval lengths
- Well-specific sequence length adaptation
- Minimum viable length detection (6 steps based on research)
- Automatic fallback with data augmentation
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import warnings
from datetime import datetime


@dataclass
class SequenceOptimizationResult:
    """Results from adaptive sequence optimization."""
    optimal_length: int
    success: bool
    sequences_created: int
    method_used: str
    fallback_applied: bool
    data_utilization: float
    recommendations: List[str]
    
    # Detailed metrics
    attempted_lengths: List[int]
    success_by_length: Dict[int, bool]
    sequence_counts_by_length: Dict[int, int]
    
    # Timing and quality
    optimization_time: float
    data_quality_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'optimal_length': self.optimal_length,
            'success': self.success,
            'sequences_created': self.sequences_created,
            'method_used': self.method_used,
            'fallback_applied': self.fallback_applied,
            'data_utilization': self.data_utilization,
            'recommendations': self.recommendations,
            'attempted_lengths': self.attempted_lengths,
            'success_by_length': self.success_by_length,
            'sequence_counts_by_length': self.sequence_counts_by_length,
            'optimization_time': self.optimization_time,
            'data_quality_score': self.data_quality_score
        }


class AdaptiveSequenceOptimizer:
    """
    Adaptive sequence length optimizer for SAITS model training with configurable Phase 2.3 features.
    
    This class implements intelligent sequence length selection and optimization
    strategies to handle the challenge of short well log intervals while maintaining
    effective model training.
    
    Key Features:
    - Progressive sequence length fallback (16 → 12 → 8 → 6 → 4)
    - Data utilization threshold monitoring
    - Research-based constraints for attention mechanisms
    - Configurable Phase 2.3 augmentation strategies (Yes/No toggle)
    
    Phase 2.3 Toggle Configuration:
    - enable_phase23_augmentation=True (default): Full advanced augmentation
      * Overlapping windows with adaptive stride
      * Property-aware interpolation with geological constraints
      * Multi-resolution sequence generation
      * Temporal jittering with geological validation
      * Cross-well feature transfer learning
    
    - enable_phase23_augmentation=False: Basic augmentation only
      * Simple overlapping windows with fixed stride
      * Basic linear interpolation for small gaps only
      * No advanced geological constraints or cross-well features
    
    Research Foundation:
    - Attention mechanisms work best with sequences ≥ 16 (Vaswani et al., 2017)
    - Transformer models show degraded performance below 8 tokens
    - SAITS benefits from longer sequences for better imputation context
    
    Usage Examples:
    >>> # Full Phase 2.3 features (default)
    >>> optimizer = AdaptiveSequenceOptimizer(enable_phase23_augmentation=True)
    >>> 
    >>> # Basic mode without Phase 2.3 features
    >>> optimizer = AdaptiveSequenceOptimizer(enable_phase23_augmentation=False)
    """
    
    def __init__(self, 
                 target_lengths: List[int] = None,
                 minimum_viable_length: int = 6,
                 minimum_sequence_count: int = 10,
                 data_utilization_threshold: float = 0.3,
                 enable_phase23_augmentation: bool = True,
                 verbose: bool = True):
        """
        Initialize the adaptive sequence optimizer.
        
        Args:
            target_lengths: Sequence lengths to try in order [16, 12, 8, 6, 4]
            minimum_viable_length: Minimum length for attention mechanisms (6 based on research)
            minimum_sequence_count: Minimum sequences required for training
            data_utilization_threshold: Minimum data utilization rate
            enable_phase23_augmentation: Enable Phase 2.3 sequence augmentation techniques
                                       (overlapping windows, multi-resolution, temporal jittering, cross-well transfer)
                                       Set to False to use basic sequence creation only
            verbose: Enable detailed logging
        """
        self.target_lengths = target_lengths or [16, 12, 8, 6, 4]
        self.minimum_viable_length = minimum_viable_length
        self.minimum_sequence_count = minimum_sequence_count
        self.data_utilization_threshold = data_utilization_threshold
        self.enable_phase23_augmentation = enable_phase23_augmentation
        self.verbose = verbose
        
        # Optimization history
        self.optimization_history = []
        
        # Research-based constraints from Darts examples
        self.attention_minimum = 8  # Minimum for effective attention mechanisms
        self.transformer_optimal = 12  # Optimal for transformer models
        self.deep_learning_minimum = 6  # Absolute minimum for deep learning
        
        if self.verbose:
            print("🎯 Phase 2.1: Adaptive Sequence Length Optimizer Initialized")
            print(f"   Target lengths: {self.target_lengths}")
            print(f"   Minimum viable: {self.minimum_viable_length}")
            print(f"   Phase 2.3 augmentation: {'Enabled' if self.enable_phase23_augmentation else 'Disabled'}")
            print(f"   Research constraints: attention≥{self.attention_minimum}, transformer≥{self.transformer_optimal}")
    
    def create_optimal_sequences(self, well_data: pd.DataFrame, 
                               well_col: str = 'WELL',
                               feature_cols: List[str] = None) -> SequenceOptimizationResult:
        """
        Create optimal sequences for well data using progressive length optimization.
        
        This implements the core strategy from the solution document:
        ```python
        def create_optimal_sequences(self, well_data):
            for length in self.target_lengths:
                try:
                    sequences = self.extract_sequences(well_data, length)
                    if len(sequences) >= self.minimum_sequence_count:
                        return sequences, length
                except Exception as e:
                    continue
            # Fallback with data augmentation
            return self.augmented_sequence_creation(well_data)
        ```
        
        Args:
            well_data: Well log dataframe
            well_col: Well identifier column
            feature_cols: Feature columns to use
            
        Returns:
            SequenceOptimizationResult with optimization details
        """
        start_time = datetime.now()
        
        if feature_cols is None:
            feature_cols = [col for col in well_data.columns if col != well_col]
        
        if self.verbose:
            print(f"\n🔄 Starting adaptive sequence optimization for {len(feature_cols)} features...")
            print(f"   Data shape: {well_data.shape}")
            print(f"   Wells: {well_data[well_col].nunique() if well_col in well_data.columns else 'Unknown'}")
        
        # Initialize result tracking
        attempted_lengths = []
        success_by_length = {}
        sequence_counts_by_length = {}
        recommendations = []
        
        # Progressive length optimization
        for length in self.target_lengths:
            attempted_lengths.append(length)
            
            if self.verbose:
                print(f"\n🎯 Attempting sequence length: {length}")
            
            try:
                # Extract sequences for this length
                sequences, metadata = self._extract_sequences(well_data, well_col, feature_cols, length)
                sequence_count = len(sequences) if sequences is not None else 0
                
                sequence_counts_by_length[length] = sequence_count
                
                if self.verbose:
                    print(f"   Extracted {sequence_count} sequences")
                
                # Check if this length meets our requirements
                if sequence_count >= self.minimum_sequence_count:
                    success_by_length[length] = True
                    
                    # Calculate data utilization
                    data_utilization = self._calculate_data_utilization(well_data, sequences, length)
                    
                    if data_utilization >= self.data_utilization_threshold:
                        # Success! This length works
                        optimization_time = (datetime.now() - start_time).total_seconds()
                        
                        if self.verbose:
                            print(f"✅ Success with length {length}!")
                            print(f"   Sequences: {sequence_count}")
                            print(f"   Data utilization: {data_utilization:.1%}")
                        
                        # Add recommendations based on research
                        if length >= self.transformer_optimal:
                            recommendations.append(f"Excellent for transformer models (length={length}≥{self.transformer_optimal})")
                        elif length >= self.attention_minimum:
                            recommendations.append(f"Good for attention mechanisms (length={length}≥{self.attention_minimum})")
                        elif length >= self.deep_learning_minimum:
                            recommendations.append(f"Adequate for deep learning (length={length}≥{self.deep_learning_minimum})")
                        else:
                            recommendations.append(f"Below optimal for deep learning (length={length}<{self.deep_learning_minimum})")
                        
                        result = SequenceOptimizationResult(
                            optimal_length=length,
                            success=True,
                            sequences_created=sequence_count,
                            method_used='progressive_optimization',
                            fallback_applied=False,
                            data_utilization=data_utilization,
                            recommendations=recommendations,
                            attempted_lengths=attempted_lengths,
                            success_by_length=success_by_length,
                            sequence_counts_by_length=sequence_counts_by_length,
                            optimization_time=optimization_time,
                            data_quality_score=self._calculate_data_quality_score(well_data, feature_cols)
                        )
                        
                        self.optimization_history.append(result)
                        return result
                    else:
                        if self.verbose:
                            print(f"   ⚠️ Low data utilization: {data_utilization:.1%} < {self.data_utilization_threshold:.1%}")
                        success_by_length[length] = False
                else:
                    if self.verbose:
                        print(f"   ❌ Insufficient sequences: {sequence_count} < {self.minimum_sequence_count}")
                    success_by_length[length] = False
                    
            except Exception as e:
                if self.verbose:
                    print(f"   ❌ Error with length {length}: {e}")
                success_by_length[length] = False
                sequence_counts_by_length[length] = 0
        
        # All standard lengths failed - apply fallback with data augmentation
        if self.verbose:
            print(f"\n🔄 Standard lengths failed, applying fallback with data augmentation...")
        
        fallback_result = self._augmented_sequence_creation(well_data, well_col, feature_cols)
        
        optimization_time = (datetime.now() - start_time).total_seconds()
        
        result = SequenceOptimizationResult(
            optimal_length=fallback_result['length'],
            success=fallback_result['success'],
            sequences_created=fallback_result['sequence_count'],
            method_used='augmented_fallback',
            fallback_applied=True,
            data_utilization=fallback_result['data_utilization'],
            recommendations=fallback_result['recommendations'],
            attempted_lengths=attempted_lengths,
            success_by_length=success_by_length,
            sequence_counts_by_length=sequence_counts_by_length,
            optimization_time=optimization_time,
            data_quality_score=self._calculate_data_quality_score(well_data, feature_cols)
        )
        
        self.optimization_history.append(result)
        return result
    
    def _extract_sequences(self, well_data: pd.DataFrame, well_col: str, 
                          feature_cols: List[str], sequence_length: int,
                          stride: int = None) -> Tuple[Optional[np.ndarray], Dict[str, Any]]:
        """Extract sequences of specified length from well data."""
        
        if stride is None:
            # Adaptive stride based on sequence length (from Darts best practices)
            stride = max(1, sequence_length // 4)
        
        sequences = []
        metadata = {
            'sequence_length': sequence_length,
            'stride': stride,
            'wells_processed': 0,
            'intervals_found': 0
        }
        
        wells = well_data[well_col].unique() if well_col in well_data.columns else ['default']
        
        for well in wells:
            if well_col in well_data.columns:
                well_df = well_data[well_data[well_col] == well].copy()
            else:
                well_df = well_data.copy()
            
            metadata['wells_processed'] += 1
            
            # Get feature data
            if not all(col in well_df.columns for col in feature_cols):
                continue
                
            well_features = well_df[feature_cols].values
            
            # Find continuous valid intervals
            valid_mask = ~np.isnan(well_features).any(axis=1)
            
            if not np.any(valid_mask):
                continue
            
            # Find interval boundaries
            breaks = [0] + (np.where(valid_mask[:-1] != valid_mask[1:])[0] + 1).tolist() + [len(valid_mask)]
            
            for i in range(len(breaks) - 1):
                start_idx = breaks[i]
                end_idx = breaks[i + 1]
                
                if valid_mask[start_idx]:  # Valid interval
                    interval_data = well_features[start_idx:end_idx]
                    interval_length = end_idx - start_idx
                    
                    metadata['intervals_found'] += 1
                    
                    # Extract sequences from this interval
                    if interval_length >= sequence_length:
                        for seq_start in range(0, interval_length - sequence_length + 1, stride):
                            seq_end = seq_start + sequence_length
                            sequence = interval_data[seq_start:seq_end]
                            
                            # Double-check no NaN values
                            if not np.isnan(sequence).any():
                                sequences.append(sequence)
        
        if sequences:
            sequences_array = np.array(sequences)
            metadata['sequences_created'] = len(sequences_array)
            metadata['final_shape'] = sequences_array.shape
            return sequences_array, metadata
        else:
            metadata['sequences_created'] = 0
            return None, metadata
    
    def _augmented_sequence_creation(self, well_data: pd.DataFrame, 
                                   well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Fallback sequence creation with optional Phase 2.3 data augmentation.
        
        This implements multiple augmentation strategies based on the toggle:
        - Phase 2.3 Enabled: All advanced strategies (overlapping windows, interpolation, 
                            multi-resolution, temporal jittering, cross-well transfer)
        - Phase 2.3 Disabled: Basic sequence creation with minimal augmentation
        """
        if self.verbose:
            augmentation_status = "with Phase 2.3 features" if self.enable_phase23_augmentation else "basic mode (Phase 2.3 disabled)"
            print(f"🔧 Implementing augmented sequence creation {augmentation_status}...")
        
        # Configure augmentation strategies based on toggle
        if self.enable_phase23_augmentation:
            # Phase 2.3: Full advanced augmentation strategies
            augmentation_strategies = [
                ('overlapping_windows', self._create_overlapping_windows),
                ('gap_interpolation', self._create_interpolated_sequences),
                ('multi_resolution', self._create_multi_resolution_sequences),
                ('temporal_jittering', self._create_temporal_jittered_sequences),
                ('cross_well_transfer', self._create_cross_well_sequences)
            ]
        else:
            # Basic mode: Only essential strategies without Phase 2.3 features
            augmentation_strategies = [
                ('basic_overlapping', self._create_basic_overlapping_sequences),
                ('basic_interpolation', self._create_basic_interpolated_sequences)
            ]
        
        best_result = {
            'length': 4,
            'success': False,
            'sequence_count': 0,
            'data_utilization': 0.0,
            'recommendations': ['All optimization strategies failed - using emergency fallback']
        }
        
        for strategy_name, strategy_func in augmentation_strategies:
            try:
                if self.verbose:
                    print(f"   Trying {strategy_name}...")
                
                result = strategy_func(well_data, well_col, feature_cols)
                
                # Ensure result has all required keys
                if 'success' not in result:
                    result['success'] = result['sequence_count'] >= self.minimum_sequence_count
                if 'recommendations' not in result:
                    result['recommendations'] = []
                
                if result['sequence_count'] >= self.minimum_sequence_count:
                    if self.verbose:
                        print(f"   ✅ {strategy_name} succeeded: {result['sequence_count']} sequences")
                    
                    result['success'] = True
                    result['recommendations'] = [f"Successfully used {strategy_name} for sequence creation"]
                    return result
                else:
                    if self.verbose:
                        print(f"   ⚠️ {strategy_name} insufficient: {result['sequence_count']} sequences")
                    
                    # Keep the best result so far
                    if result['sequence_count'] > best_result['sequence_count']:
                        best_result = result.copy()
                        best_result['success'] = False
                        best_result['recommendations'] = [f"Best attempt: {strategy_name} with {result['sequence_count']} sequences"]
                        
            except Exception as e:
                if self.verbose:
                    print(f"   ❌ {strategy_name} failed: {e}")
                continue
        
        return best_result
    
    def _create_basic_overlapping_sequences(self, well_data: pd.DataFrame, 
                                          well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Basic overlapping window strategy without Phase 2.3 advanced features.
        Uses simple overlapping with fixed stride for basic augmentation.
        
        This method is used when enable_phase23_augmentation=False to provide
        minimal sequence augmentation without advanced geological constraints.
        """
        try:
            if self.verbose:
                print("   📋 Trying basic overlapping windows...")
            
            sequences = []
            targets = []
            
            for well_name in well_data[well_col].unique():
                well_subset = well_data[well_data[well_col] == well_name].copy()
                
                if len(well_subset) < self.minimum_viable_length:
                    continue
                
                # Simple overlapping with stride = sequence_length // 2
                stride = max(1, self.target_lengths[0] // 2)
                
                for start_idx in range(0, len(well_subset) - self.target_lengths[0] + 1, stride):
                    end_idx = start_idx + self.target_lengths[0]
                    sequence = well_subset.iloc[start_idx:end_idx][feature_cols].values
                    
                    if not np.isnan(sequence).all():
                        sequences.append(sequence)
                        targets.append(well_name)
            
            return {
                'sequences': np.array(sequences) if sequences else np.array([]),
                'targets': targets,
                'method': 'basic_overlapping',
                'sequence_count': len(sequences),
                'success': len(sequences) >= self.minimum_sequence_count,
                'length': self.target_lengths[0],
                'data_utilization': len(sequences) / max(1, len(well_data))
            }
            
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Basic overlapping failed: {str(e)}")
            return {'sequences': np.array([]), 'targets': [], 'method': 'basic_overlapping', 
                   'sequence_count': 0, 'success': False, 'error': str(e),
                   'length': 4, 'data_utilization': 0.0}
    
    def _create_basic_interpolated_sequences(self, well_data: pd.DataFrame, 
                                           well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Basic interpolation strategy without Phase 2.3 advanced features.
        Uses simple linear interpolation for small gaps only.
        """
        try:
            if self.verbose:
                print("   📋 Trying basic interpolation...")
            
            sequences = []
            targets = []
            
            for well_name in well_data[well_col].unique():
                well_subset = well_data[well_data[well_col] == well_name].copy()
                
                if len(well_subset) < self.minimum_viable_length:
                    continue
                
                # Simple linear interpolation for small gaps (max 3 consecutive NaN values)
                for col in feature_cols:
                    series = well_subset[col]
                    # Only interpolate small gaps
                    mask = series.isna()
                    gap_sizes = mask.groupby((~mask).cumsum()).sum()
                    small_gaps = gap_sizes <= 3
                    
                    if small_gaps.any():
                        well_subset[col] = series.interpolate(method='linear', limit=3)
                
                # Create sequences from interpolated data
                for start_idx in range(len(well_subset) - self.target_lengths[0] + 1):
                    end_idx = start_idx + self.target_lengths[0]
                    sequence = well_subset.iloc[start_idx:end_idx][feature_cols].values
                    
                    # Only accept sequences with minimal missing data
                    if np.isnan(sequence).sum() / sequence.size < 0.1:  # Less than 10% missing
                        sequences.append(sequence)
                        targets.append(well_name)
            
            return {
                'sequences': np.array(sequences) if sequences else np.array([]),
                'targets': targets,
                'method': 'basic_interpolation',
                'sequence_count': len(sequences),
                'success': len(sequences) >= self.minimum_sequence_count,
                'length': self.target_lengths[0],
                'data_utilization': len(sequences) / max(1, len(well_data))
            }
            
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Basic interpolation failed: {str(e)}")
            return {'sequences': np.array([]), 'targets': [], 'method': 'basic_interpolation', 
                   'sequence_count': 0, 'success': False, 'error': str(e),
                   'length': 4, 'data_utilization': 0.0}
    
    def _create_overlapping_windows(self, well_data: pd.DataFrame, 
                                  well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """Create sequences with overlapping windows (minimal stride)."""
        
        # Try progressively smaller lengths with minimal stride
        for length in [8, 6, 4]:
            sequences, metadata = self._extract_sequences(
                well_data, well_col, feature_cols, length, stride=1
            )
            
            if sequences is not None and len(sequences) > 0:
                data_utilization = self._calculate_data_utilization(well_data, sequences, length)
                
                return {
                    'length': length,
                    'sequence_count': len(sequences),
                    'data_utilization': data_utilization,
                    'method': 'overlapping_windows'
                }
        
        return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'overlapping_windows'}
    
    def _create_interpolated_sequences(self, well_data: pd.DataFrame, 
                                     well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """Create sequences by interpolating small gaps."""
        
        # Apply gap interpolation and then try sequence creation
        interpolated_data = self._interpolate_small_gaps(well_data, feature_cols, max_gap_size=3)
        
        for length in [12, 8, 6, 4]:
            sequences, metadata = self._extract_sequences(
                interpolated_data, well_col, feature_cols, length
            )
            
            if sequences is not None and len(sequences) > 0:
                data_utilization = self._calculate_data_utilization(interpolated_data, sequences, length)
                
                return {
                    'length': length,
                    'sequence_count': len(sequences),
                    'data_utilization': data_utilization,
                    'method': 'gap_interpolation'
                }
        
        return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'gap_interpolation'}
    
    def _create_multi_resolution_sequences(self, well_data: pd.DataFrame, 
                                         well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """Create sequences using multi-resolution approach."""
        
        # Combine sequences of different lengths
        all_sequences = []
        
        for length in [8, 6, 4]:
            sequences, metadata = self._extract_sequences(
                well_data, well_col, feature_cols, length
            )
            
            if sequences is not None:
                # Pad shorter sequences to match the longest length (8)
                if length < 8:
                    # Repeat last value to pad
                    padding_needed = 8 - length
                    padded_sequences = []
                    
                    for seq in sequences:
                        last_values = seq[-1:].repeat(padding_needed, axis=0)
                        padded_seq = np.concatenate([seq, last_values], axis=0)
                        padded_sequences.append(padded_seq)
                    
                    sequences = np.array(padded_sequences)
                
                all_sequences.extend(sequences)
        
        if all_sequences:
            sequences_array = np.array(all_sequences)
            data_utilization = len(all_sequences) / max(1, len(well_data))
            
            return {
                'length': 8,  # Standardized length
                'sequence_count': len(all_sequences),
                'data_utilization': data_utilization,
                'method': 'multi_resolution'
            }
        
        return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'multi_resolution'}
    
    def _create_cross_well_sequences(self, well_data: pd.DataFrame, 
                                   well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """Create sequences using cross-well feature transfer."""
        
        if well_col not in well_data.columns:
            return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'cross_well_transfer'}
        
        wells = well_data[well_col].unique()
        
        # Find the well with best data quality
        best_well = None
        best_completeness = 0
        
        for well in wells:
            well_df = well_data[well_data[well_col] == well]
            completeness = 1 - well_df[feature_cols].isnull().sum().sum() / (len(well_df) * len(feature_cols))
            
            if completeness > best_completeness:
                best_completeness = completeness
                best_well = well
        
        if best_well is not None and best_completeness > 0.7:
            # Use the best well's data to augment others
            best_well_data = well_data[well_data[well_col] == best_well]
            
            for length in [12, 8, 6, 4]:
                sequences, metadata = self._extract_sequences(
                    best_well_data, well_col, feature_cols, length
                )
                
                if sequences is not None and len(sequences) > 0:
                    data_utilization = best_completeness  # Use completeness as utilization metric
                    
                    return {
                        'length': length,
                        'sequence_count': len(sequences),
                        'data_utilization': data_utilization,
                        'method': 'cross_well_transfer'
                    }
        
        return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'cross_well_transfer'}
    
    def _create_temporal_jittered_sequences(self, well_data: pd.DataFrame, 
                                          well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Create sequences using temporal jittering with geological constraints.
        
        Applies small random perturbations to sequence values while respecting
        geological constraints for each log type.
        """
        if self.verbose:
            print("   🎲 Applying temporal jittering with geological constraints...")
        
        # Define geological constraints for different log types
        geological_constraints = {
            'RHOB': {'min': 1.5, 'max': 3.5, 'jitter_percent': 0.02},  # ±2% for density
            'GR': {'min': 0, 'max': 300, 'jitter_percent': 0.05},      # ±5% for gamma ray
            'NPHI': {'min': -0.15, 'max': 0.6, 'jitter_percent': 0.03}, # ±3% for neutron porosity
            'DT': {'min': 40, 'max': 200, 'jitter_percent': 0.04},     # ±4% for sonic
            'CALI': {'min': 6, 'max': 20, 'jitter_percent': 0.03},     # ±3% for caliper
            'SP': {'min': -150, 'max': 50, 'jitter_percent': 0.05},    # ±5% for spontaneous potential
            'PHOTOELECTRIC': {'min': 1, 'max': 6, 'jitter_percent': 0.03}  # ±3% for photoelectric
        }
        
        # First, try to create base sequences
        base_sequences = None
        base_length = None
        
        for length in [8, 6, 4]:
            sequences, metadata = self._extract_sequences(
                well_data, well_col, feature_cols, length
            )
            
            if sequences is not None and len(sequences) > 0:
                base_sequences = sequences
                base_length = length
                break
        
        if base_sequences is None or len(base_sequences) == 0:
            if self.verbose:
                print("   ⚠️ No base sequences found for temporal jittering")
            return {'length': 4, 'sequence_count': 0, 'data_utilization': 0.0, 'method': 'temporal_jittering'}
        
        # Apply temporal jittering to create augmented sequences
        jittered_sequences = []
        jittered_sequences.extend(base_sequences)  # Include original sequences
        
        # Create multiple jittered versions (2-3 versions per original sequence)
        num_jittered_versions = min(3, max(1, 20 // len(base_sequences)))  # Adaptive number of versions
        
        for original_seq in base_sequences:
            for version in range(num_jittered_versions):
                try:
                    jittered_seq = self._apply_temporal_jitter(
                        original_seq, feature_cols, geological_constraints
                    )
                    
                    if jittered_seq is not None:
                        jittered_sequences.append(jittered_seq)
                        
                except Exception as e:
                    if self.verbose:
                        print(f"   ⚠️ Jittering failed for sequence {version}: {e}")
                    continue
        
        if len(jittered_sequences) > len(base_sequences):
            sequences_array = np.array(jittered_sequences)
            data_utilization = len(jittered_sequences) / max(1, len(well_data))
            
            if self.verbose:
                print(f"   ✅ Created {len(jittered_sequences)} jittered sequences from {len(base_sequences)} base sequences")
            
            return {
                'length': base_length,
                'sequence_count': len(jittered_sequences),
                'data_utilization': data_utilization,
                'method': 'temporal_jittering'
            }
        else:
            if self.verbose:
                print("   ⚠️ Temporal jittering did not increase sequence count")
            return {'length': base_length, 'sequence_count': len(base_sequences), 'data_utilization': 0.0, 'method': 'temporal_jittering'}
    
    def _apply_temporal_jitter(self, sequence: np.ndarray, feature_cols: List[str], 
                              geological_constraints: Dict[str, Dict]) -> np.ndarray:
        """
        Apply temporal jittering to a single sequence with geological constraints.
        
        Args:
            sequence: Original sequence data (sequence_length, num_features)
            feature_cols: List of feature column names
            geological_constraints: Constraints for each log type
            
        Returns:
            Jittered sequence or None if jittering fails
        """
        if sequence.ndim != 2:
            return None
            
        jittered_seq = sequence.copy()
        
        for i, col_name in enumerate(feature_cols):
            if i >= sequence.shape[1]:
                break
                
            # Get constraints for this feature
            col_upper = col_name.upper()
            constraints = None
            
            # Find matching constraint (handle variations in naming)
            for constraint_key in geological_constraints.keys():
                if constraint_key in col_upper or col_upper in constraint_key:
                    constraints = geological_constraints[constraint_key]
                    break
            
            # Use default constraints if no specific ones found
            if constraints is None:
                constraints = {'min': -1000, 'max': 1000, 'jitter_percent': 0.03}
            
            # Calculate feature range for jittering
            feature_values = sequence[:, i]
            valid_mask = ~np.isnan(feature_values)
            
            if not valid_mask.any():
                continue
                
            feature_range = np.ptp(feature_values[valid_mask])  # Peak-to-peak range
            if feature_range == 0:
                feature_range = np.abs(np.mean(feature_values[valid_mask]))
            
            # Calculate jitter magnitude
            jitter_magnitude = feature_range * constraints['jitter_percent']
            
            # Apply jittering to valid values only
            for j in range(len(feature_values)):
                if valid_mask[j]:
                    # Generate random jitter
                    jitter = np.random.normal(0, jitter_magnitude * 0.5)  # Use half magnitude as std
                    jittered_value = feature_values[j] + jitter
                    
                    # Apply geological constraints
                    jittered_value = np.clip(jittered_value, constraints['min'], constraints['max'])
                    
                    # Additional validation: ensure jittered value is reasonable
                    original_value = feature_values[j]
                    max_change = abs(original_value) * constraints['jitter_percent'] * 2  # Allow up to 2x jitter percent
                    
                    if abs(jittered_value - original_value) <= max_change:
                        jittered_seq[j, i] = jittered_value
                    # If change is too large, keep original value
        
        return jittered_seq
    
    def _interpolate_small_gaps(self, data: pd.DataFrame, feature_cols: List[str], 
                               max_gap_size: int = 3) -> pd.DataFrame:
        """Interpolate small gaps in the data using physics-aware methods."""
        
        interpolated_data = data.copy()
        
        for col in feature_cols:
            if col not in data.columns:
                continue
            
            # Find gaps
            is_missing = data[col].isnull()
            
            if is_missing.any():
                # Use pandas interpolation with method selection based on data type
                # Linear interpolation for continuous properties (RHOB, NPHI)
                # Forward fill for categorical-like properties
                
                if col.upper() in ['RHOB', 'NPHI', 'DT', 'PHOTOELECTRIC']:
                    # Continuous properties - use linear interpolation
                    interpolated_data[col] = data[col].interpolate(method='linear', limit=max_gap_size)
                elif col.upper() in ['GR', 'CALI', 'SP']:
                    # Smooth properties - use spline interpolation
                    try:
                        interpolated_data[col] = data[col].interpolate(method='spline', order=2, limit=max_gap_size)
                    except:
                        # Fallback to linear if spline fails
                        interpolated_data[col] = data[col].interpolate(method='linear', limit=max_gap_size)
                else:
                    # Default - use linear interpolation
                    interpolated_data[col] = data[col].interpolate(method='linear', limit=max_gap_size)
        
        return interpolated_data
    
    def _calculate_data_utilization(self, original_data: pd.DataFrame, 
                                  sequences: np.ndarray, sequence_length: int) -> float:
        """Calculate how much of the original data is being utilized in sequences."""
        
        if sequences is None or len(sequences) == 0:
            return 0.0
        
        # Calculate the number of data points used in sequences
        total_sequence_points = len(sequences) * sequence_length
        
        # Calculate total available data points
        total_data_points = len(original_data)
        
        # Calculate utilization ratio
        utilization = min(1.0, total_sequence_points / max(1, total_data_points))
        
        return utilization
    
    def _calculate_data_quality_score(self, data: pd.DataFrame, feature_cols: List[str]) -> float:
        """Calculate overall data quality score."""
        
        if len(feature_cols) == 0 or len(data) == 0:
            return 0.0
        
        # Completeness score
        completeness = 1 - data[feature_cols].isnull().sum().sum() / (len(data) * len(feature_cols))
        
        # Continuity score (based on longest continuous intervals)
        continuity_scores = []
        
        for col in feature_cols:
            if col in data.columns:
                valid_mask = ~data[col].isnull()
                
                if valid_mask.any():
                    # Find longest continuous interval
                    max_continuous = 0
                    current_continuous = 0
                    
                    for is_valid in valid_mask:
                        if is_valid:
                            current_continuous += 1
                            max_continuous = max(max_continuous, current_continuous)
                        else:
                            current_continuous = 0
                    
                    continuity = min(1.0, max_continuous / len(data))
                    continuity_scores.append(continuity)
        
        avg_continuity = np.mean(continuity_scores) if continuity_scores else 0.0
        
        # Combined quality score
        quality_score = 0.7 * completeness + 0.3 * avg_continuity
        
        return quality_score
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of all optimization attempts."""
        
        if not self.optimization_history:
            return {'total_optimizations': 0, 'success_rate': 0.0}
        
        successful = sum(1 for result in self.optimization_history if result.success)
        total = len(self.optimization_history)
        
        avg_sequences = np.mean([result.sequences_created for result in self.optimization_history])
        avg_length = np.mean([result.optimal_length for result in self.optimization_history])
        avg_utilization = np.mean([result.data_utilization for result in self.optimization_history])
        
        return {
            'total_optimizations': total,
            'successful_optimizations': successful,
            'success_rate': successful / total,
            'average_sequences_created': avg_sequences,
            'average_optimal_length': avg_length,
            'average_data_utilization': avg_utilization,
            'fallback_usage_rate': sum(1 for result in self.optimization_history if result.fallback_applied) / total
        }


# Convenience functions for backward compatibility
def optimize_sequence_length(well_data: pd.DataFrame, 
                           well_col: str = 'WELL',
                           feature_cols: List[str] = None,
                           target_lengths: List[int] = None,
                           verbose: bool = True) -> SequenceOptimizationResult:
    """
    Convenience function for sequence length optimization.
    
    Args:
        well_data: Well log dataframe
        well_col: Well identifier column
        feature_cols: Feature columns
        target_lengths: Sequence lengths to try
        verbose: Enable logging
        
    Returns:
        SequenceOptimizationResult
    """
    optimizer = AdaptiveSequenceOptimizer(
        target_lengths=target_lengths,
        verbose=verbose
    )
    
    return optimizer.create_optimal_sequences(well_data, well_col, feature_cols)


def get_recommended_sequence_length(well_data: pd.DataFrame,
                                  well_col: str = 'WELL',
                                  feature_cols: List[str] = None) -> Tuple[int, Dict[str, Any]]:
    """
    Get recommended sequence length and optimization details.
    
    Args:
        well_data: Well log dataframe
        well_col: Well identifier column  
        feature_cols: Feature columns
        
    Returns:
        Tuple of (optimal_length, optimization_details)
    """
    result = optimize_sequence_length(well_data, well_col, feature_cols, verbose=False)
    
    return result.optimal_length, result.to_dict()


# Export main classes and functions
__all__ = [
    'AdaptiveSequenceOptimizer',
    'SequenceOptimizationResult',
    'optimize_sequence_length',
    'get_recommended_sequence_length'
]