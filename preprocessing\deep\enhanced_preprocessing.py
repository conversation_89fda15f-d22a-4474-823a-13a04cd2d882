"""
Enhanced preprocessing module for ML log prediction.
Integrates advanced techniques from cp_preconditioning with deep learning optimizations.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from scipy import interpolate
from scipy.ndimage import uniform_filter1d
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings
from enum import Enum

class PropertyType(Enum):
    """Enumeration of well log property types for interpolation strategy selection."""
    CONTINUOUS = "continuous"  # Linear properties like RHOB, NPHI, DT
    SMOOTH = "smooth"         # Smooth properties like GR, CALI, SP
    CATEGORICAL = "categorical" # Categorical properties like facies
    RESISTIVITY = "resistivity" # Special handling for resistivity logs

class PropertyAwareInterpolator:
    """
    Property-aware interpolation class for well log data.
    Implements different interpolation strategies based on log property types
    with geological constraint validation.
    """
    
    def __init__(self, enable_geological_validation: bool = True):
        """
        Initialize the property-aware interpolator.
        
        Args:
            enable_geological_validation: Whether to apply geological constraints
        """
        self.enable_geological_validation = enable_geological_validation
        
        # Define property type mappings
        self.property_mappings = {
            # Continuous properties - linear interpolation
            'RHOB': PropertyType.CONTINUOUS,
            'NPHI': PropertyType.CONTINUOUS, 
            'DT': PropertyType.CONTINUOUS,
            'PHOTOELECTRIC': PropertyType.CONTINUOUS,
            'PEF': PropertyType.CONTINUOUS,
            'DENSITY': PropertyType.CONTINUOUS,
            'NEUTRON': PropertyType.CONTINUOUS,
            'POROSITY': PropertyType.CONTINUOUS,
            
            # Smooth properties - cubic spline interpolation
            'GR': PropertyType.SMOOTH,
            'CALI': PropertyType.SMOOTH,
            'SP': PropertyType.SMOOTH,
            'GAMMA': PropertyType.SMOOTH,
            'CALIPER': PropertyType.SMOOTH,
            
            # Resistivity properties - special log-space interpolation
            'RT': PropertyType.RESISTIVITY,
            'RES': PropertyType.RESISTIVITY,
            'RESISTIVITY': PropertyType.RESISTIVITY,
            'ILD': PropertyType.RESISTIVITY,
            'ILM': PropertyType.RESISTIVITY,
            'LLD': PropertyType.RESISTIVITY,
            'LLS': PropertyType.RESISTIVITY,
        }
        
        # Geological constraints for common properties
        self.geological_constraints = {
            'RHOB': (1.0, 3.5),      # g/cm³
            'NPHI': (-0.15, 1.0),    # fraction
            'DT': (40, 700),         # μs/ft
            'GR': (0, 500),          # API
            'CALI': (4, 20),         # inches
            'SP': (-200, 200),       # mV
            'RT': (0.1, 10000),      # ohm-m
            'PHOTOELECTRIC': (1.0, 10.0),  # barns/electron
        }
    
    def _detect_property_type(self, property_name: str) -> PropertyType:
        """
        Automatically detect property type from column name.
        
        Args:
            property_name: Name of the property/column
            
        Returns:
            PropertyType enum value
        """
        prop_upper = property_name.upper()
        
        # Direct mapping
        if prop_upper in self.property_mappings:
            return self.property_mappings[prop_upper]
        
        # Pattern matching for common variations
        if any(keyword in prop_upper for keyword in ['RHOB', 'DENSITY', 'DEN']):
            return PropertyType.CONTINUOUS
        elif any(keyword in prop_upper for keyword in ['NPHI', 'NEUTRON', 'NEU']):
            return PropertyType.CONTINUOUS
        elif any(keyword in prop_upper for keyword in ['DT', 'SONIC', 'TRANSIT']):
            return PropertyType.CONTINUOUS
        elif any(keyword in prop_upper for keyword in ['GR', 'GAMMA']):
            return PropertyType.SMOOTH
        elif any(keyword in prop_upper for keyword in ['CALI', 'CALIPER']):
            return PropertyType.SMOOTH
        elif any(keyword in prop_upper for keyword in ['SP', 'SPONTANEOUS']):
            return PropertyType.SMOOTH
        elif any(keyword in prop_upper for keyword in ['RES', 'RT', 'ILD', 'ILM', 'LLD', 'LLS']):
            return PropertyType.RESISTIVITY
        elif any(keyword in prop_upper for keyword in ['FACIES', 'LITH', 'FORMATION']):
            return PropertyType.CATEGORICAL
        else:
            # Default to continuous for unknown properties
            return PropertyType.CONTINUOUS
    
    def validate_geological_constraints(self, data: np.ndarray, property_name: str) -> np.ndarray:
        """
        Apply geological constraints to interpolated values.
        
        Args:
            data: Interpolated data array
            property_name: Name of the property for constraint lookup
            
        Returns:
            Data array with geological constraints applied
        """
        if not self.enable_geological_validation:
            return data
        
        prop_upper = property_name.upper()
        
        # Find matching constraint
        constraint_key = None
        for key in self.geological_constraints:
            if key in prop_upper or prop_upper in key:
                constraint_key = key
                break
        
        if constraint_key is None:
            return data  # No constraints defined
        
        min_val, max_val = self.geological_constraints[constraint_key]
        
        # Apply constraints
        constrained_data = data.copy()
        constrained_data = np.clip(constrained_data, min_val, max_val)
        
        # Count violations for reporting
        violations = np.sum((data < min_val) | (data > max_val))
        if violations > 0:
            print(f"⚠️ Applied geological constraints to {property_name}: "
                  f"{violations} values clipped to [{min_val}, {max_val}]")
        
        return constrained_data
    
    def interpolate_property_aware(self, data: np.ndarray, property_name: str, 
                                 max_gap_length: int = 3) -> np.ndarray:
        """
        Apply property-aware interpolation based on detected property type.
        
        Args:
            data: Input data array with potential NaN values
            property_name: Name of the property for type detection
            max_gap_length: Maximum gap length to interpolate
            
        Returns:
            Interpolated data array with geological constraints applied
        """
        if data.size == 0:
            return data
        
        property_type = self._detect_property_type(property_name)
        
        # Apply appropriate interpolation method
        if property_type == PropertyType.CONTINUOUS:
            result = self._linear_interpolate_gaps(data, max_gap_length)
        elif property_type == PropertyType.SMOOTH:
            result = self._cubic_interpolate_gaps(data, max_gap_length)
        elif property_type == PropertyType.RESISTIVITY:
            result = self._resistivity_interpolate_gaps(data, max_gap_length)
        elif property_type == PropertyType.CATEGORICAL:
            result = self._categorical_interpolate_gaps(data, max_gap_length)
        else:
            # Default to linear
            result = self._linear_interpolate_gaps(data, max_gap_length)
        
        # Apply geological constraints
        result = self.validate_geological_constraints(result, property_name)
        
        return result
    
    def _linear_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 3) -> np.ndarray:
        """Linear interpolation for continuous properties."""
        result = data.copy()
        is_missing = np.isnan(data)

        if not is_missing.any():
            return result

        valid_indices = np.where(~is_missing)[0]

        if len(valid_indices) < 2:
            return result

        missing_indices = np.where(is_missing)[0]

        for missing_idx in missing_indices:
            left_valid = valid_indices[valid_indices < missing_idx]
            right_valid = valid_indices[valid_indices > missing_idx]

            if len(left_valid) > 0 and len(right_valid) > 0:
                left_idx = left_valid[-1]
                right_idx = right_valid[0]

                gap_length = right_idx - left_idx - 1
                if gap_length <= max_gap_length:
                    left_val = data[left_idx]
                    right_val = data[right_idx]
                    weight = (missing_idx - left_idx) / (right_idx - left_idx)
                    result[missing_idx] = left_val + weight * (right_val - left_val)

        return result
    
    def _cubic_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 3) -> np.ndarray:
        """Cubic spline interpolation for smooth properties."""
        try:
            from scipy.interpolate import interp1d

            result = data.copy()
            is_missing = np.isnan(data)

            if not is_missing.any():
                return result

            valid_indices = np.where(~is_missing)[0]

            if len(valid_indices) < 4:
                return self._linear_interpolate_gaps(data, max_gap_length)

            valid_data = data[valid_indices]
            interp_func = interp1d(valid_indices, valid_data, kind='cubic',
                                 bounds_error=False, fill_value='extrapolate')

            missing_indices = np.where(is_missing)[0]

            for missing_idx in missing_indices:
                if (missing_idx > valid_indices[0] and missing_idx < valid_indices[-1]):
                    left_valid = valid_indices[valid_indices < missing_idx]
                    right_valid = valid_indices[valid_indices > missing_idx]

                    if len(left_valid) > 0 and len(right_valid) > 0:
                        gap_length = right_valid[0] - left_valid[-1] - 1
                        if gap_length <= max_gap_length:
                            result[missing_idx] = interp_func(missing_idx)

            return result

        except ImportError:
            return self._linear_interpolate_gaps(data, max_gap_length)
        except Exception:
            return self._linear_interpolate_gaps(data, max_gap_length)
    
    def _resistivity_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 3) -> np.ndarray:
        """Log-space interpolation for resistivity properties."""
        result = data.copy()
        is_missing = np.isnan(data)

        if not is_missing.any():
            return result

        # Convert to log space for interpolation
        log_data = np.log10(np.maximum(data, 0.01))  # Avoid log(0)
        
        # Apply linear interpolation in log space
        log_result = self._linear_interpolate_gaps(log_data, max_gap_length)
        
        # Convert back to linear space
        result = np.power(10, log_result)
        
        return result
    
    def _categorical_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 1) -> np.ndarray:
        """Forward/backward fill for categorical properties."""
        result = data.copy()
        is_missing = np.isnan(data)

        if not is_missing.any():
            return result

        # Forward fill
        last_valid = None
        for i in range(len(result)):
            if not is_missing[i]:
                last_valid = result[i]
            elif last_valid is not None:
                # Check gap length
                gap_start = i
                gap_end = i
                while gap_end < len(result) and is_missing[gap_end]:
                    gap_end += 1
                
                if gap_end - gap_start <= max_gap_length:
                    result[i] = last_valid

        # Backward fill for remaining gaps
        next_valid = None
        for i in range(len(result) - 1, -1, -1):
            if not np.isnan(result[i]):
                next_valid = result[i]
            elif next_valid is not None and np.isnan(result[i]):
                # Check gap length
                gap_end = i
                gap_start = i
                while gap_start >= 0 and np.isnan(result[gap_start]):
                    gap_start -= 1
                
                if gap_end - gap_start <= max_gap_length:
                    result[i] = next_valid

        return result
    
    def interpolate_property(self, data: np.ndarray, property_name: str, 
                           max_gap_length: int = 3) -> np.ndarray:
        """
        Public interface for property-aware interpolation.
        
        Args:
            data: Input data array with potential NaN values
            property_name: Name of the property for type detection
            max_gap_length: Maximum gap length to interpolate
            
        Returns:
            Interpolated data array with geological constraints applied
        """
        return self.interpolate_property_aware(data, property_name, max_gap_length)
    
    def _apply_geological_constraints(self, value: float, property_name: str) -> float:
        """
        Apply geological constraints to a single value.
        
        Args:
            value: Input value to constrain
            property_name: Name of the property for constraint lookup
            
        Returns:
            Constrained value
        """
        if not self.enable_geological_validation:
            return value
        
        prop_upper = property_name.upper()
        
        # Find matching constraint
        constraint_key = None
        for key in self.geological_constraints:
            if key in prop_upper or prop_upper in key:
                constraint_key = key
                break
        
        if constraint_key is None:
            return value  # No constraints defined
        
        min_val, max_val = self.geological_constraints[constraint_key]
        return np.clip(value, min_val, max_val)

class EnhancedLogPreprocessor:
    """
    Enhanced preprocessing class that combines cp_preconditioning techniques
    with deep learning best practices for well log data.
    """
    
    def __init__(self, 
                 winsorize_percentiles: Tuple[float, float] = (0.01, 0.99),
                 normalization_method: str = 'standard',
                 sequence_len: int = 64,
                 sequence_stride: int = 32,
                 missing_rate: float = 0.2,
                 random_seed: int = 42,
                 enable_property_aware_interpolation: bool = True):
        """
        Initialize the enhanced preprocessor.
        
        Args:
            winsorize_percentiles: Percentiles for outlier clipping (lower, upper)
            normalization_method: 'standard', 'robust', or 'minmax'
            sequence_len: Length of sequences for deep learning models
            sequence_stride: Stride between sequence start points
            missing_rate: Rate of missing values to introduce for training
            random_seed: Random seed for reproducibility
            enable_property_aware_interpolation: Whether to use property-aware interpolation
        """
        self.winsorize_percentiles = winsorize_percentiles
        self.normalization_method = normalization_method
        self.sequence_len = sequence_len
        self.sequence_stride = sequence_stride
        self.missing_rate = missing_rate
        self.random_seed = random_seed
        self.enable_property_aware_interpolation = enable_property_aware_interpolation
        
        # Storage for preprocessing parameters
        self.scalers = {}
        self.outlier_bounds = {}
        self.preprocessing_stats = {}
        
        # Initialize property-aware interpolator
        if self.enable_property_aware_interpolation:
            self.property_interpolator = PropertyAwareInterpolator(enable_geological_validation=True)
        else:
            self.property_interpolator = None
        
        np.random.seed(random_seed)
    
    def winsorize_outliers(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """
        Apply statistical winsorization to remove outliers.
        More robust than hard-coded thresholds.
        
        Args:
            df: Input dataframe
            columns: Columns to winsorize
            
        Returns:
            DataFrame with outliers winsorized
        """
        df_clean = df.copy()
        
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found, skipping winsorization")
                continue
                
            # Get valid (non-NaN) data for percentile calculation
            valid_data = df[col].dropna()
            
            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping winsorization")
                continue
            
            # Calculate percentile bounds
            lower_bound, upper_bound = np.quantile(valid_data, self.winsorize_percentiles)
            
            # Store bounds for reporting
            self.outlier_bounds[col] = {
                'lower': lower_bound,
                'upper': upper_bound,
                'original_range': (valid_data.min(), valid_data.max())
            }
            
            # Apply winsorization
            outlier_mask = (df[col] < lower_bound) | (df[col] > upper_bound)
            outlier_count = outlier_mask.sum()
            
            # Set outliers to NaN (more conservative than clipping)
            df_clean.loc[outlier_mask, col] = np.nan
            
            print(f"Winsorized '{col}': bounds=[{lower_bound:.3f}, {upper_bound:.3f}], "
                  f"removed {outlier_count} outliers ({outlier_count/len(df)*100:.1f}%)")
        
        return df_clean
    
    def normalize_data_enhanced(self, df: pd.DataFrame, columns: List[str]) -> Tuple[pd.DataFrame, Dict]:
        """
        Enhanced normalization with better missing value handling and multi-well consistency.
        
        Args:
            df: Input dataframe
            columns: Columns to normalize
            
        Returns:
            Tuple of (normalized_df, scalers_dict)
        """
        df_scaled = df.copy()
        scalers = {}
        
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found, skipping normalization")
                continue
            
            # Get valid data for fitting scaler
            valid_data = df[col].dropna()
            
            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping normalization")
                scalers[col] = None
                continue
            
            # Choose scaler based on method
            if self.normalization_method == 'standard':
                scaler = StandardScaler()
            elif self.normalization_method == 'robust':
                scaler = RobustScaler()
            else:
                raise ValueError(f"Unknown normalization method: {self.normalization_method}")
            
            # Fit scaler on valid data only
            scaler.fit(valid_data.values.reshape(-1, 1))
            
            # Transform all data (preserves NaN values)
            df_scaled[col] = scaler.transform(df[[col]])
            scalers[col] = scaler
            
            # Store normalization statistics
            self.preprocessing_stats[col] = {
                'original_mean': valid_data.mean(),
                'original_std': valid_data.std(),
                'valid_count': len(valid_data),
                'total_count': len(df[col]),
                'missing_rate': (len(df[col]) - len(valid_data)) / len(df[col])
            }
            
            print(f"Normalized '{col}': method={self.normalization_method}, "
                  f"valid_data={len(valid_data)}/{len(df[col])} "
                  f"({(1-self.preprocessing_stats[col]['missing_rate'])*100:.1f}%)")
        
        self.scalers = scalers
        return df_scaled, scalers
    
    def get_valid_intervals(self, data_array: np.ndarray, columns_to_check: List[int] = None) -> List[Tuple[int, int]]:
        """
        Identify continuous intervals without NaN values.
        Critical for sequence-based deep learning models.

        Args:
            data_array: Input array (samples x features)
            columns_to_check: List of column indices to check for validity.
                            If None, checks all columns.

        Returns:
            List of (start_idx, end_idx) tuples for valid intervals
        """
        # Determine which columns to check for validity
        if columns_to_check is None:
            check_array = data_array
        else:
            check_array = data_array[:, columns_to_check]

        # Check which rows have all valid (non-NaN) values in the specified columns
        all_valid = ~np.isnan(check_array).any(axis=1)

        # Find breakpoints where validity changes
        breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]

        valid_intervals = []
        for i in range(len(breaks) - 1):
            start_idx = breaks[i]
            end_idx = breaks[i + 1]

            # Only store intervals that have valid data
            if all_valid[start_idx]:
                valid_intervals.append((start_idx, end_idx))

        return valid_intervals
    
    def create_sequences_enhanced(self, df: pd.DataFrame, well_col: str,
                                feature_cols: List[str]) -> Tuple[np.ndarray, List[Dict]]:
        """
        Create sequences with comprehensive debugging and intelligent fallback.
        Phase 1 Implementation: Enhanced method debugging with detailed logging.

        Args:
            df: Input dataframe
            well_col: Column name for well identifier
            feature_cols: Feature column names

        Returns:
            Tuple of (sequences_array, metadata_list)
        """
        print("\n🔄 Phase 1: Enhanced sequence creation with comprehensive debugging...")

        # Phase 1.1: Enhanced Method Debugging Framework
        debug_info = self._debug_enhanced_methods(df, well_col, feature_cols)
        print("✅ Phase 1.1 completed successfully")

        # Phase 1.2: Progressive fallback with detailed error handling
        print("🔄 Phase 1.2: Progressive fallback with detailed error handling")
        methods = ['enhanced_optimized', 'enhanced_basic', 'basic_adaptive', 'emergency_fallback']

        for i, method_name in enumerate(methods, 1):
            try:
                print(f"🔧 Phase 1.2.{i}: Attempting method: {method_name}")

                if method_name == 'enhanced_optimized':
                    result = self._try_enhanced_optimized(df, well_col, feature_cols, debug_info)
                elif method_name == 'enhanced_basic':
                    result = self._try_enhanced_basic(df, well_col, feature_cols, debug_info)
                elif method_name == 'basic_adaptive':
                    result = self._try_basic_adaptive(df, well_col, feature_cols, debug_info)
                else:  # emergency_fallback
                    result = self._emergency_fallback(df, well_col, feature_cols, debug_info)

                sequences_array, metadata = result

                if sequences_array.shape[0] > 0:
                    print(f"✅ Phase 1.2.{i}: Method {method_name} succeeded: {sequences_array.shape}")
                    print(f"   Created {len(sequences_array)} sequences")

                    # Phase 2.2: Data Interpolation and Gap Filling (if needed)
                    if method_name in ['enhanced_optimized', 'enhanced_basic']:
                        print("🔄 Phase 2.2: Data Interpolation and Gap Filling")
                        sequences_array = self._apply_intelligent_gap_filling(sequences_array, feature_cols)
                        print("✅ Phase 2.2 completed successfully")

                    self._log_success(method_name, sequences_array.shape, debug_info)
                    return sequences_array, metadata
                else:
                    print(f"⚠️ Phase 1.2.{i}: Method {method_name} produced no sequences")

            except Exception as e:
                print(f"❌ Phase 1.2.{i}: Method {method_name} failed: {str(e)}")
                error_info = {
                    'method': method_name,
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'parameters': {
                        'sequence_len': self.sequence_len,
                        'stride': self.sequence_stride,
                        'df_shape': df.shape,
                        'feature_cols': feature_cols
                    }
                }
                self._log_failure(method_name, error_info)
                continue
        
        # All methods failed - implement emergency fallback
        print("❌ All enhanced methods failed, implementing emergency measures...")
        return self._implement_emergency_fallback(df, well_col, feature_cols)
    
    def _debug_enhanced_methods(self, df: pd.DataFrame, well_col: str, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Phase 1.1: Comprehensive debugging framework for enhanced methods.
        Validates parameters, dependencies, and data characteristics.
        """
        print("🔍 Phase 1.1: Enhanced Method Debugging Framework")
        
        debug_info = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'data_characteristics': {},
            'parameter_validation': {},
            'dependency_status': {},
            'memory_status': {},
            'recommendations': []
        }
        
        # Data characteristics analysis
        debug_info['data_characteristics'] = {
            'df_shape': df.shape,
            'wells_count': df[well_col].nunique() if well_col in df.columns else 0,
            'feature_cols_count': len(feature_cols),
            'feature_cols_available': [col for col in feature_cols if col in df.columns],
            'missing_feature_cols': [col for col in feature_cols if col not in df.columns],
            'overall_completeness': (1 - df[feature_cols].isnull().sum().sum() / (df.shape[0] * len(feature_cols))) if feature_cols else 0
        }
        
        # Parameter validation
        debug_info['parameter_validation'] = {
            'sequence_len': self.sequence_len,
            'sequence_stride': self.sequence_stride,
            'sequence_len_valid': isinstance(self.sequence_len, int) and self.sequence_len > 0,
            'stride_valid': isinstance(self.sequence_stride, int) and self.sequence_stride > 0,
            'stride_reasonable': self.sequence_stride <= self.sequence_len
        }
        
        # Dependency status check
        try:
            import torch
            debug_info['dependency_status']['torch_available'] = True
            debug_info['dependency_status']['torch_version'] = torch.__version__
            debug_info['dependency_status']['cuda_available'] = torch.cuda.is_available()
        except ImportError:
            debug_info['dependency_status']['torch_available'] = False
            
        try:
            import numpy as np
            debug_info['dependency_status']['numpy_available'] = True
            debug_info['dependency_status']['numpy_version'] = np.__version__
        except ImportError:
            debug_info['dependency_status']['numpy_available'] = False
        
        # Memory status (if torch available)
        if debug_info['dependency_status'].get('torch_available', False):
            try:
                import torch
                if torch.cuda.is_available():
                    debug_info['memory_status']['gpu_memory_allocated'] = torch.cuda.memory_allocated()
                    debug_info['memory_status']['gpu_memory_reserved'] = torch.cuda.memory_reserved()
                    debug_info['memory_status']['gpu_memory_available'] = torch.cuda.get_device_properties(0).total_memory
                else:
                    debug_info['memory_status']['gpu_available'] = False
            except Exception as e:
                debug_info['memory_status']['gpu_error'] = str(e)
        
        # Generate recommendations
        if debug_info['data_characteristics']['overall_completeness'] < 0.5:
            debug_info['recommendations'].append("Low data completeness - consider data augmentation")
        
        if self.sequence_len > 32 and debug_info['data_characteristics']['overall_completeness'] < 0.7:
            debug_info['recommendations'].append("Reduce sequence length for sparse data")
            
        if not debug_info['parameter_validation']['stride_reasonable']:
            debug_info['recommendations'].append("Reduce stride - currently larger than sequence length")
        
        print(f"📊 Data: {debug_info['data_characteristics']['df_shape']} shape, "
              f"{debug_info['data_characteristics']['wells_count']} wells, "
              f"{debug_info['data_characteristics']['overall_completeness']:.1%} complete")
        
        return debug_info

    def _apply_intelligent_gap_filling(self, sequences: np.ndarray, feature_cols: List[str]) -> np.ndarray:
        """
        Phase 2.2: Data Interpolation and Gap Filling
        Fill short gaps to create longer continuous segments using property-aware interpolation.

        Args:
            sequences: Input sequences array of shape (n_sequences, sequence_length, n_features)
            feature_cols: List of feature column names

        Returns:
            Enhanced sequences with gaps filled using property-specific methods
        """
        if sequences.size == 0:
            return sequences

        enhanced_sequences = sequences.copy()
        n_sequences, seq_len, n_features = sequences.shape

        # Use property-aware interpolation if enabled
        if self.property_interpolator is not None:
            print("🔧 Using property-aware interpolation for gap filling...")
            
            for seq_idx in range(n_sequences):
                for feat_idx, feature_name in enumerate(feature_cols):
                    if feat_idx >= n_features:
                        continue

                    sequence_data = enhanced_sequences[seq_idx, :, feat_idx]

                    # Find missing values (NaN)
                    is_missing = np.isnan(sequence_data)

                    if is_missing.any() and not is_missing.all():
                        # Use property-aware interpolation
                        try:
                            interpolated_data = self.property_interpolator.interpolate_property(
                                data=sequence_data,
                                property_name=feature_name,
                                max_gap_length=3
                            )
                            enhanced_sequences[seq_idx, :, feat_idx] = interpolated_data
                        except Exception as e:
                            print(f"⚠️ Property-aware interpolation failed for {feature_name}: {e}")
                            # Fallback to basic linear interpolation
                            enhanced_sequences[seq_idx, :, feat_idx] = self._linear_interpolate_gaps(sequence_data)
        else:
            # Fallback to basic interpolation methods
            print("🔧 Using basic interpolation methods for gap filling...")
            
            for seq_idx in range(n_sequences):
                for feat_idx, feature_name in enumerate(feature_cols):
                    if feat_idx >= n_features:
                        continue

                    sequence_data = enhanced_sequences[seq_idx, :, feat_idx]

                    # Find missing values (NaN)
                    is_missing = np.isnan(sequence_data)

                    if is_missing.any() and not is_missing.all():
                        # Apply physics-aware interpolation based on feature type
                        if feature_name.upper() in ['RHOB', 'NPHI', 'DT', 'PHOTOELECTRIC']:
                            # Continuous properties - use linear interpolation
                            enhanced_sequences[seq_idx, :, feat_idx] = self._linear_interpolate_gaps(sequence_data)
                        elif feature_name.upper() in ['GR', 'CALI', 'SP']:
                            # Smooth properties - use cubic interpolation
                            enhanced_sequences[seq_idx, :, feat_idx] = self._cubic_interpolate_gaps(sequence_data)
                        else:
                            # Default - use linear interpolation
                            enhanced_sequences[seq_idx, :, feat_idx] = self._linear_interpolate_gaps(sequence_data)

        return enhanced_sequences

    def _linear_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 3) -> np.ndarray:
        """Linear interpolation for continuous properties."""
        result = data.copy()
        is_missing = np.isnan(data)

        if not is_missing.any():
            return result

        # Find valid (non-NaN) indices
        valid_indices = np.where(~is_missing)[0]

        if len(valid_indices) < 2:
            # Not enough valid points for interpolation
            return result

        # Interpolate missing values
        missing_indices = np.where(is_missing)[0]

        for missing_idx in missing_indices:
            # Find nearest valid points
            left_valid = valid_indices[valid_indices < missing_idx]
            right_valid = valid_indices[valid_indices > missing_idx]

            if len(left_valid) > 0 and len(right_valid) > 0:
                left_idx = left_valid[-1]
                right_idx = right_valid[0]

                # Check gap length
                gap_length = right_idx - left_idx - 1
                if gap_length <= max_gap_length:
                    # Linear interpolation
                    left_val = data[left_idx]
                    right_val = data[right_idx]
                    weight = (missing_idx - left_idx) / (right_idx - left_idx)
                    result[missing_idx] = left_val + weight * (right_val - left_val)

        return result

    def _cubic_interpolate_gaps(self, data: np.ndarray, max_gap_length: int = 3) -> np.ndarray:
        """Cubic interpolation for smooth properties with fallback to linear."""
        try:
            from scipy.interpolate import interp1d

            result = data.copy()
            is_missing = np.isnan(data)

            if not is_missing.any():
                return result

            valid_indices = np.where(~is_missing)[0]

            if len(valid_indices) < 4:
                # Not enough points for cubic interpolation, fallback to linear
                return self._linear_interpolate_gaps(data, max_gap_length)

            # Create interpolation function
            valid_data = data[valid_indices]
            interp_func = interp1d(valid_indices, valid_data, kind='cubic',
                                 bounds_error=False, fill_value='extrapolate')

            # Fill missing values within reasonable gaps
            missing_indices = np.where(is_missing)[0]

            for missing_idx in missing_indices:
                # Check if within interpolation range and gap size
                if (missing_idx > valid_indices[0] and missing_idx < valid_indices[-1]):
                    # Find gap size
                    left_valid = valid_indices[valid_indices < missing_idx]
                    right_valid = valid_indices[valid_indices > missing_idx]

                    if len(left_valid) > 0 and len(right_valid) > 0:
                        gap_length = right_valid[0] - left_valid[-1] - 1
                        if gap_length <= max_gap_length:
                            result[missing_idx] = interp_func(missing_idx)

            return result

        except ImportError:
            # Scipy not available, fallback to linear interpolation
            return self._linear_interpolate_gaps(data, max_gap_length)
        except Exception:
            # Any other error, fallback to linear interpolation
            return self._linear_interpolate_gaps(data, max_gap_length)
    
    def _try_enhanced_optimized(self, df: pd.DataFrame, well_col: str,
                               feature_cols: List[str], debug_info: Dict[str, Any]) -> Tuple[np.ndarray, List[Dict]]:
        """Try enhanced optimized sequence creation with AdaptiveSequenceCreator."""
        import signal
        import time

        def timeout_handler(signum, frame):
            raise TimeoutError("Enhanced optimized method timed out after 30 seconds")

        try:
            print("🔧 Importing AdaptiveSequenceCreator...")
            from .adaptive_sequence_creator import AdaptiveSequenceCreator
            print("✅ AdaptiveSequenceCreator imported successfully")

            # Set timeout for the operation
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(30)  # 30 second timeout

            try:
                print("🔧 Creating adaptive sequence creator...")
                # Create adaptive sequence creator with validated parameters
                adaptive_creator = AdaptiveSequenceCreator(
                    verbose=True,
                    enable_diagnostics=True
                )
                print("✅ AdaptiveSequenceCreator created successfully")

                print("🔧 Starting adaptive sequence creation...")
                # Use adaptive sequence creation with progressive fallback
                result = adaptive_creator.create_sequences_adaptive(
                    df=df,
                    well_col=well_col,
                    feature_cols=feature_cols,
                    sequence_length=self.sequence_len,
                    stride=self.sequence_stride
                )
                print("✅ Adaptive sequence creation completed")

                sequences_array = result.sequences
                metadata = [result.metadata] if not isinstance(result.metadata, list) else result.metadata

                return sequences_array, metadata

            finally:
                signal.alarm(0)  # Cancel the timeout

        except ImportError as e:
            print(f"⚠️ AdaptiveSequenceCreator import failed: {e}")
            raise ImportError(f"Enhanced optimized method unavailable: {e}")
        except TimeoutError as e:
            print(f"⏰ Enhanced optimized method timed out: {e}")
            raise Exception(f"Enhanced optimized method timed out - falling back to next method")
        except Exception as e:
            print(f"❌ Enhanced optimized method failed: {e}")
            import traceback
            print(f"   Full traceback: {traceback.format_exc()}")
            raise Exception(f"Enhanced optimized execution failed: {e}")
    
    def _try_enhanced_basic(self, df: pd.DataFrame, well_col: str,
                           feature_cols: List[str], debug_info: Dict[str, Any]) -> Tuple[np.ndarray, List[Dict]]:
        """Try enhanced basic sequence creation without external dependencies."""
        # Use the built-in enhanced logic without external imports
        print("🔧 Using built-in enhanced sequence creation...")
        
        # Validate inputs based on debug info
        if not debug_info['parameter_validation']['sequence_len_valid']:
            raise ValueError(f"Invalid sequence length: {self.sequence_len}")
            
        if not debug_info['parameter_validation']['stride_valid']:
            raise ValueError(f"Invalid stride: {self.sequence_stride}")
        
        # Apply recommendations if needed
        adjusted_seq_len = self.sequence_len
        adjusted_stride = self.sequence_stride
        
        if debug_info['data_characteristics']['overall_completeness'] < 0.5 and self.sequence_len > 16:
            adjusted_seq_len = max(8, self.sequence_len // 2)
            print(f"📏 Adjusted sequence length from {self.sequence_len} to {adjusted_seq_len} due to sparse data")
        
        if not debug_info['parameter_validation']['stride_reasonable']:
            adjusted_stride = max(1, adjusted_seq_len // 4)
            print(f"📏 Adjusted stride from {self.sequence_stride} to {adjusted_stride}")
        
        # Create sequences using enhanced built-in method
        sequences = []
        metadata = []
        
        wells = df[well_col].unique()
        for well in wells:
            well_df = df[df[well_col] == well].copy()
            well_data = well_df[feature_cols].values
            
            if len(well_data) == 0:
                continue
            
            # Enhanced valid interval detection
            valid_intervals = self.get_valid_intervals(well_data)
            
            for start_idx, end_idx in valid_intervals:
                interval_length = end_idx - start_idx
                
                if interval_length < adjusted_seq_len:
                    continue
                
                # Create sequences with adjusted parameters
                for seq_start in range(0, interval_length - adjusted_seq_len + 1, adjusted_stride):
                    seq_end = seq_start + adjusted_seq_len
                    sequence_data = well_data[start_idx + seq_start:start_idx + seq_end]
                    
                    if sequence_data.shape[0] == adjusted_seq_len and not np.isnan(sequence_data).any():
                        sequences.append(sequence_data)
                        metadata.append({
                            'well': well,
                            'interval_start': start_idx,
                            'seq_start': seq_start,
                            'method': 'enhanced_basic'
                        })
        
        if sequences:
            sequences_array = np.array(sequences)
            print(f"✅ Enhanced basic created {len(sequences)} sequences")
            return sequences_array, metadata
        else:
            return np.empty((0, adjusted_seq_len, len(feature_cols))), []
    
    def _try_basic_adaptive(self, df: pd.DataFrame, well_col: str,
                           feature_cols: List[str], debug_info: Dict[str, Any]) -> Tuple[np.ndarray, List[Dict]]:
        """Try basic adaptive sequence creation with progressive length reduction."""
        print("🔧 Using basic adaptive sequence creation...")
        
        # Progressive sequence length reduction
        target_lengths = [self.sequence_len, 16, 12, 8, 6, 4]
        
        for target_length in target_lengths:
            if target_length > self.sequence_len:
                continue
                
            print(f"🎯 Trying sequence length: {target_length}")
            
            sequences = []
            metadata = []
            
            wells = df[well_col].unique()
            for well in wells:
                well_df = df[df[well_col] == well].copy()
                well_data = well_df[feature_cols].values
                
                if len(well_data) < target_length:
                    continue
                
                # Simple valid sequence detection
                for i in range(0, len(well_data) - target_length + 1, max(1, target_length // 4)):
                    sequence_data = well_data[i:i + target_length]
                    
                    if not np.isnan(sequence_data).any():
                        sequences.append(sequence_data)
                        metadata.append({
                            'well': well,
                            'start_idx': i,
                            'method': 'basic_adaptive',
                            'target_length': target_length
                        })
            
            if len(sequences) > 0:
                sequences_array = np.array(sequences)
                print(f"✅ Basic adaptive created {len(sequences)} sequences with length {target_length}")
                
                # Update sequence length for consistency
                self.sequence_len = target_length
                return sequences_array, metadata
        
        # No sequences created with any length
        return np.empty((0, 4, len(feature_cols))), []
    
    def _emergency_fallback(self, df: pd.DataFrame, well_col: str,
                           feature_cols: List[str], debug_info: Dict[str, Any]) -> Tuple[np.ndarray, List[Dict]]:
        """Emergency fallback with minimal sequence length and maximum flexibility."""
        print("🚨 Emergency fallback - minimal requirements")
        
        min_seq_length = 3
        sequences = []
        metadata = []
        
        wells = df[well_col].unique()
        for well in wells:
            well_df = df[df[well_col] == well].copy()
            
            # Process each feature separately if needed
            for col in feature_cols:
                if col not in well_df.columns:
                    continue
                    
                valid_data = well_df[col].dropna()
                if len(valid_data) >= min_seq_length:
                    # Create very short sequences from single features
                    for i in range(0, len(valid_data) - min_seq_length + 1):
                        # Create sequence with repeated single feature
                        single_feature_seq = valid_data.iloc[i:i + min_seq_length].values.reshape(-1, 1)
                        
                        # Expand to match feature count by repeating
                        expanded_seq = np.tile(single_feature_seq, (1, len(feature_cols)))
                        sequences.append(expanded_seq)
                        
                        metadata.append({
                            'well': well,
                            'feature': col,
                            'start_idx': i,
                            'method': 'emergency_fallback',
                            'seq_length': min_seq_length
                        })
                        
                        # Limit sequences per well to avoid memory issues
                        if len(sequences) >= 100:
                            break
                    
                    if len(sequences) >= 100:
                        break
        
        if sequences:
            sequences_array = np.array(sequences)
            print(f"🚨 Emergency fallback created {len(sequences)} sequences (length {min_seq_length})")
            self.sequence_len = min_seq_length
            return sequences_array, metadata
        else:
            return np.empty((0, min_seq_length, len(feature_cols))), []
    
    def _implement_emergency_fallback(self, df: pd.DataFrame, well_col: str, feature_cols: List[str]) -> Tuple[np.ndarray, List[Dict]]:
        """Final emergency implementation when all else fails."""
        print("🆘 Final emergency fallback implementation")
        
        # Create dummy sequences if absolutely nothing else works
        dummy_sequences = np.random.randn(1, 4, len(feature_cols)) * 0.1
        dummy_metadata = [{
            'method': 'emergency_dummy',
            'warning': 'Created dummy sequences due to complete failure',
            'original_request': {
                'sequence_len': self.sequence_len,
                'stride': self.sequence_stride,
                'df_shape': df.shape
            }
        }]
        
        print("🆘 Created dummy sequences as last resort")
        return dummy_sequences, dummy_metadata
    
    def _log_success(self, method_name: str, shape: Tuple[int, ...], debug_info: Dict[str, Any]):
        """Log successful sequence creation."""
        print(f"📝 Success Log - Method: {method_name}")
        print(f"   Shape: {shape}")
        print(f"   Execution time: {pd.Timestamp.now().isoformat()}")
        print(f"   Data completeness: {debug_info['data_characteristics']['overall_completeness']:.1%}")
    
    def _log_failure(self, method_name: str, error_info: Dict[str, Any]):
        """Log failed sequence creation attempt."""
        print(f"📝 Failure Log - Method: {method_name}")
        print(f"   Error: {error_info['error']}")
        print(f"   Error Type: {error_info['error_type']}")
        print(f"   Parameters: {error_info['parameters']}")
    
    def _create_sequences_basic_fallback(self, df: pd.DataFrame, well_col: str,
                                       feature_cols: List[str]) -> Tuple[np.ndarray, List[Dict]]:
        """
        Basic fallback sequence creation method.
        """
        print("🔧 Using basic fallback sequence creation...")
        
        all_sequences = []
        metadata = []
        df_reset = df.reset_index()
        wells = df_reset[well_col].unique()
        
        for well in wells:
            well_df = df_reset[df_reset[well_col] == well].copy()
            well_data = well_df[feature_cols].values
            
            # Get valid intervals
            valid_intervals = self.get_valid_intervals(well_data)
            
            for start_idx, end_idx in valid_intervals:
                interval_length = end_idx - start_idx
                
                # Skip intervals too short for sequences
                if interval_length < self.sequence_len:
                    continue
                
                # Get the part of the dataframe for this interval
                interval_df = well_df.iloc[start_idx:end_idx]
                
                # Create sequences within this valid interval
                current_pos = 0
                while current_pos + self.sequence_len <= len(interval_df):
                    sequence_data = interval_df.iloc[current_pos:current_pos + self.sequence_len]
                    all_sequences.append(sequence_data[feature_cols].values)
                    
                    metadata.append({
                        'well': well,
                        'original_indices': sequence_data['index'].tolist(),
                        'sequence_id': len(all_sequences) - 1
                    })
                    
                    current_pos += self.sequence_stride
        
        if not all_sequences:
            print("⚠️ No sequences created with basic fallback method")
            empty_sequences = np.empty((0, self.sequence_len, len(feature_cols)), dtype=np.float32)
            return empty_sequences, []
        
        sequences_array = np.array(all_sequences)
        print(f"✅ Basic fallback created {len(sequences_array)} sequences (shape: {sequences_array.shape})")
        
        return sequences_array, metadata

    def introduce_realistic_missingness(self, sequences: np.ndarray) -> np.ndarray:
        """
        Introduce realistic missing patterns for well log data.
        Uses chunked missing patterns that better reflect real-world scenarios.

        Args:
            sequences: Input sequences array (n_sequences, seq_len, n_features)

        Returns:
            Sequences with missing values introduced
        """
        sequences_with_missing = sequences.copy()
        n_sequences, seq_len, n_features = sequences.shape

        total_elements = np.prod(sequences.shape)
        n_missing = int(total_elements * self.missing_rate)

        # Strategy: Mix of random and chunked missing patterns
        # 30% random missing (sensor noise, bad readings)
        # 70% chunked missing (tool failures, bad hole conditions)

        random_missing = int(n_missing * 0.3)
        chunked_missing = n_missing - random_missing

        # Validate inputs for random missing values
        if total_elements == 0:
            print("Warning: No elements available for missing value introduction")
            return sequences_with_missing
        
        if random_missing > total_elements:
            print(f"Warning: Requested random missing ({random_missing}) exceeds total elements ({total_elements})")
            random_missing = min(random_missing, total_elements)
            chunked_missing = max(0, n_missing - random_missing)
        
        # Random missing values
        if random_missing > 0:
            random_indices = np.random.choice(total_elements, random_missing, replace=False)
        else:
            random_indices = np.array([], dtype=int)

        # Chunked missing values (more realistic for well logs)
        chunk_indices = []
        
        # Validate sequence length for chunking
        if seq_len < 3:
            # For very short sequences, use only random missing
            print(f"Warning: Sequence length {seq_len} too short for chunking, using only random missing")
            chunk_sizes = []
        else:
            # Ensure chunk sizes are valid
            max_chunk_size = min(15, seq_len // 2, seq_len - 1)
            min_chunk_size = min(3, max_chunk_size)
            
            if max_chunk_size < min_chunk_size:
                print(f"Warning: Cannot create chunks for seq_len={seq_len}, using only random missing")
                chunk_sizes = []
            else:
                chunk_sizes = np.random.randint(min_chunk_size, max_chunk_size + 1, size=max(1, chunked_missing//8))

        for chunk_size in chunk_sizes:
            if len(chunk_indices) >= chunked_missing:
                break
            
            # Validate chunk size against sequence length
            if chunk_size >= seq_len:
                continue

            # Validate array dimensions for random selection
            if n_sequences <= 0 or n_features <= 0:
                print(f"Warning: Invalid dimensions n_sequences={n_sequences}, n_features={n_features}")
                continue
                
            # Random sequence and feature
            seq_idx = np.random.randint(0, n_sequences)
            feat_idx = np.random.randint(0, n_features)
            
            # Ensure valid range for start position
            max_start_pos = seq_len - chunk_size
            if max_start_pos <= 0:
                continue
            start_pos = np.random.randint(0, max_start_pos)

            # Add chunk indices
            for i in range(chunk_size):
                if start_pos + i < seq_len:
                    flat_idx = (seq_idx * seq_len * n_features +
                              (start_pos + i) * n_features + feat_idx)
                    chunk_indices.append(flat_idx)

        # Combine all missing indices
        all_missing_indices = list(set(list(random_indices) + chunk_indices[:chunked_missing]))

        # Apply missing values
        flat_view = sequences_with_missing.flatten()
        flat_view[all_missing_indices] = np.nan
        sequences_with_missing = flat_view.reshape(sequences.shape)

        actual_missing_rate = len(all_missing_indices) / total_elements
        print(f"Introduced {actual_missing_rate:.1%} missing values "
              f"({len(all_missing_indices)} elements)")
        print(f"Pattern: {random_missing} random + {len(all_missing_indices)-random_missing} chunked")

        return sequences_with_missing

    def validate_preprocessing_quality(self, sequences: np.ndarray,
                                     sequences_missing: np.ndarray) -> Dict:
        """
        Validate the quality of preprocessing for deep learning models.

        Args:
            sequences: Original sequences
            sequences_missing: Sequences with missing values

        Returns:
            Dictionary with quality metrics
        """
        quality_metrics = {}

        # Check for NaN values in original sequences
        nan_count_orig = np.isnan(sequences).sum()
        quality_metrics['original_nan_count'] = int(nan_count_orig)
        quality_metrics['original_nan_rate'] = float(nan_count_orig / np.prod(sequences.shape))

        # Check missing value introduction
        nan_count_missing = np.isnan(sequences_missing).sum()
        quality_metrics['missing_nan_count'] = int(nan_count_missing)
        quality_metrics['missing_nan_rate'] = float(nan_count_missing / np.prod(sequences_missing.shape))

        # Check data distribution (should be normalized)
        valid_data = sequences[~np.isnan(sequences)]
        if len(valid_data) > 0:
            quality_metrics['data_mean'] = float(np.mean(valid_data))
            quality_metrics['data_std'] = float(np.std(valid_data))
            quality_metrics['data_range'] = (float(np.min(valid_data)), float(np.max(valid_data)))

        # Check sequence continuity
        quality_metrics['total_sequences'] = sequences.shape[0]
        quality_metrics['sequence_length'] = sequences.shape[1]
        quality_metrics['n_features'] = sequences.shape[2]

        # Gradient-friendly checks
        if len(valid_data) > 0:
            quality_metrics['gradient_friendly'] = {
                'no_extreme_values': bool(np.abs(valid_data).max() < 10),
                'reasonable_std': bool(0.5 < np.std(valid_data) < 2.0),
                'no_infinite_values': bool(np.isfinite(valid_data).all())
            }

        return quality_metrics

    def get_preprocessing_report(self) -> str:
        """
        Generate a comprehensive preprocessing report.

        Returns:
            Formatted report string
        """
        report = []
        report.append("=" * 60)
        report.append(" ENHANCED PREPROCESSING REPORT")
        report.append("=" * 60)

        # Configuration
        report.append(f"\n📋 Configuration:")
        report.append(f"   • Winsorization percentiles: {self.winsorize_percentiles}")
        report.append(f"   • Normalization method: {self.normalization_method}")
        report.append(f"   • Sequence length: {self.sequence_len}")
        report.append(f"   • Sequence stride: {self.sequence_stride}")
        report.append(f"   • Missing rate: {self.missing_rate}")

        # Outlier bounds
        if self.outlier_bounds:
            report.append(f"\n🎯 Outlier Detection (Winsorization):")
            for col, bounds in self.outlier_bounds.items():
                orig_range = bounds['original_range']
                new_range = (bounds['lower'], bounds['upper'])
                report.append(f"   • {col}: {orig_range[0]:.3f}-{orig_range[1]:.3f} → "
                            f"{new_range[0]:.3f}-{new_range[1]:.3f}")

        # Normalization stats
        if self.preprocessing_stats:
            report.append(f"\n📊 Normalization Statistics:")
            for col, stats in self.preprocessing_stats.items():
                report.append(f"   • {col}: mean={stats['original_mean']:.3f}, "
                            f"std={stats['original_std']:.3f}, "
                            f"valid={stats['valid_count']}/{stats['total_count']} "
                            f"({(1-stats['missing_rate'])*100:.1f}%)")

        report.append("\n✅ Enhanced preprocessing completed successfully!")
        return "\n".join(report)


def enhanced_preprocessing_pipeline(df: pd.DataFrame,
                                  feature_cols: List[str],
                                  target_col: str,
                                  sequence_len: int = 64,
                                  sequence_stride: int = 32,
                                  missing_rate: float = 0.2,
                                  normalization_method: str = 'standard',
                                  winsorize_percentiles: Tuple[float, float] = (0.01, 0.99),
                                  random_seed: int = 42) -> Tuple[np.ndarray, np.ndarray, Dict, str]:
    """
    Complete enhanced preprocessing pipeline for deep learning models.
    Integrates cp_preconditioning techniques with deep learning best practices.

    Args:
        df: Input dataframe with well log data
        feature_cols: List of feature column names
        target_col: Target column name
        sequence_len: Length of sequences for deep learning
        sequence_stride: Stride between sequence starts
        missing_rate: Rate of missing values to introduce
        normalization_method: 'standard' or 'robust'
        winsorize_percentiles: Percentiles for outlier removal
        random_seed: Random seed for reproducibility

    Returns:
        Tuple of (clean_sequences, missing_sequences, scalers, report)
    """
    print("🚀 Starting Enhanced Preprocessing Pipeline...")

    # Initialize preprocessor
    preprocessor = EnhancedLogPreprocessor(
        winsorize_percentiles=winsorize_percentiles,
        normalization_method=normalization_method,
        sequence_len=sequence_len,
        sequence_stride=sequence_stride,
        missing_rate=missing_rate,
        random_seed=random_seed
    )

    # Step 1: Winsorize outliers
    print("\n🎯 Step 1: Statistical outlier removal (winsorization)...")
    all_cols = feature_cols + [target_col]
    df_winsorized = preprocessor.winsorize_outliers(df, all_cols)

    # Step 2: Enhanced normalization
    print("\n📊 Step 2: Enhanced normalization...")
    df_normalized, scalers = preprocessor.normalize_data_enhanced(df_winsorized, all_cols)

    # Step 3: Create sequences with valid interval detection
    print("\n🔗 Step 3: Intelligent sequence creation...")
    sequences, metadata = preprocessor.create_sequences_enhanced(
        df_normalized, 'WELL', all_cols
    )

    # Step 4: Introduce realistic missing patterns
    print("\n❓ Step 4: Realistic missing value introduction...")
    sequences_missing = preprocessor.introduce_realistic_missingness(sequences)

    # Step 5: Quality validation
    print("\n✅ Step 5: Quality validation...")
    quality_metrics = preprocessor.validate_preprocessing_quality(sequences, sequences_missing)

    # Generate report
    report = preprocessor.get_preprocessing_report()

    # Add quality metrics to report
    report += f"\n\n📈 Quality Metrics:"
    report += f"\n   • Original NaN rate: {quality_metrics['original_nan_rate']:.1%}"
    report += f"\n   • Missing NaN rate: {quality_metrics['missing_nan_rate']:.1%}"
    report += f"\n   • Data mean: {quality_metrics.get('data_mean', 'N/A'):.3f}"
    report += f"\n   • Data std: {quality_metrics.get('data_std', 'N/A'):.3f}"
    report += f"\n   • Total sequences: {quality_metrics['total_sequences']}"

    if 'gradient_friendly' in quality_metrics:
        gf = quality_metrics['gradient_friendly']
        report += f"\n   • Gradient-friendly: {all(gf.values())}"

    print(report)

    return sequences, sequences_missing, scalers, report


# Phase 2: Data Quality Enhancement Functions
def validate_and_clean_log_data(df: pd.DataFrame, log_ranges: Dict[str, Tuple[float, float]]) -> pd.DataFrame:
    """
    Enhanced data validation and cleaning with winsorization for out-of-range values.
    
    Args:
        df: Input dataframe with well log data
        log_ranges: Dictionary mapping log names to (min_val, max_val) tuples
                   e.g., {'GR': (0, 300), 'NPHI': (-0.15, 1.0)}
    
    Returns:
        Cleaned dataframe with out-of-range values winsorized
    """
    cleaned_df = df.copy()
    
    for log_name, (min_val, max_val) in log_ranges.items():
        if log_name in cleaned_df.columns:
            # Identify out-of-range values
            out_of_range = (cleaned_df[log_name] < min_val) | (cleaned_df[log_name] > max_val)
            
            # Log issues
            if out_of_range.sum() > 0:
                print(f"Warning: {out_of_range.sum()} {log_name} values out of range [{min_val}, {max_val}]")
            
            # Apply winsorization (clipping)
            cleaned_df[log_name] = np.clip(cleaned_df[log_name], min_val, max_val)
        else:
            print(f"Warning: Column '{log_name}' not found in dataframe")
    
    return cleaned_df


def improve_well_quality(well_data: pd.DataFrame, target_score: float = 0.5) -> pd.DataFrame:
    """
    Improve well data quality through outlier removal, gap interpolation, and noise smoothing.
    
    Args:
        well_data: Input well data for a single well
        target_score: Target quality score to achieve
    
    Returns:
        Improved well data
    """
    improved_data = well_data.copy()
    
    # Calculate initial quality score
    current_score = calculate_quality_score(improved_data)
    print(f"Initial quality score: {current_score:.3f}")
    
    if current_score < target_score:
        print(f"Applying quality improvement strategies to reach target score: {target_score}")
        
        # Apply quality improvement strategies
        improved_data = remove_extreme_outliers(improved_data)
        improved_data = interpolate_small_gaps(improved_data)
        improved_data = smooth_noisy_sections(improved_data)
        
        # Calculate final quality score
        final_score = calculate_quality_score(improved_data)
        print(f"Final quality score: {final_score:.3f}")
    
    return improved_data


def calculate_quality_score(data: pd.DataFrame) -> float:
    """
    Calculate a quality score for well data based on completeness and consistency.
    
    Args:
        data: Well data dataframe
    
    Returns:
        Quality score between 0 and 1
    """
    if data.empty:
        return 0.0
    
    # Completeness score (1 - missing rate)
    completeness = 1 - data.isnull().sum().sum() / (data.shape[0] * data.shape[1])
    
    # Consistency score (based on standard deviation)
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) == 0:
        return completeness
    
    # Calculate normalized standard deviation for each column
    consistency_scores = []
    for col in numeric_cols:
        valid_data = data[col].dropna()
        if len(valid_data) > 1:
            # Normalize by range to get relative variability
            data_range = valid_data.max() - valid_data.min()
            if data_range > 0:
                normalized_std = valid_data.std() / data_range
                # Convert to score (lower std = higher score)
                consistency_score = max(0, 1 - normalized_std)
                consistency_scores.append(consistency_score)
    
    if consistency_scores:
        consistency = np.mean(consistency_scores)
    else:
        consistency = 1.0
    
    # Combine completeness and consistency (weighted average)
    quality_score = 0.7 * completeness + 0.3 * consistency
    return min(1.0, max(0.0, quality_score))


def remove_extreme_outliers(data: pd.DataFrame, z_threshold: float = 3.5) -> pd.DataFrame:
    """
    Remove extreme outliers using modified Z-score method.
    
    Args:
        data: Input dataframe
        z_threshold: Z-score threshold for outlier detection
    
    Returns:
        Data with extreme outliers removed (set to NaN)
    """
    cleaned_data = data.copy()
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        valid_data = data[col].dropna()
        if len(valid_data) > 0:
            # Calculate modified Z-score using median and MAD
            median = valid_data.median()
            mad = np.median(np.abs(valid_data - median))
            
            if mad > 0:
                modified_z_scores = 0.6745 * (data[col] - median) / mad
                outlier_mask = np.abs(modified_z_scores) > z_threshold
                
                outlier_count = outlier_mask.sum()
                if outlier_count > 0:
                    print(f"Removed {outlier_count} extreme outliers from {col}")
                    cleaned_data.loc[outlier_mask, col] = np.nan
    
    return cleaned_data


def interpolate_small_gaps(data: pd.DataFrame, max_gap_size: int = 5) -> pd.DataFrame:
    """
    Interpolate small gaps in the data using linear interpolation.
    
    Args:
        data: Input dataframe
        max_gap_size: Maximum gap size to interpolate
    
    Returns:
        Data with small gaps interpolated
    """
    interpolated_data = data.copy()
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        # Find gaps
        is_missing = data[col].isnull()
        
        if is_missing.any():
            # Find consecutive missing value groups
            missing_groups = []
            start = None
            
            for i, missing in enumerate(is_missing):
                if missing and start is None:
                    start = i
                elif not missing and start is not None:
                    missing_groups.append((start, i - 1))
                    start = None
            
            # Handle case where missing values go to the end
            if start is not None:
                missing_groups.append((start, len(is_missing) - 1))
            
            # Interpolate small gaps
            gaps_filled = 0
            for start_idx, end_idx in missing_groups:
                gap_size = end_idx - start_idx + 1
                if gap_size <= max_gap_size:
                    # Use linear interpolation
                    interpolated_data[col].iloc[start_idx:end_idx+1] = (
                        interpolated_data[col].interpolate(method='linear').iloc[start_idx:end_idx+1]
                    )
                    gaps_filled += 1
            
            if gaps_filled > 0:
                print(f"Interpolated {gaps_filled} small gaps in {col}")
    
    return interpolated_data


def smooth_noisy_sections(data: pd.DataFrame, window_size: int = 5) -> pd.DataFrame:
    """
    Smooth noisy sections using a uniform filter.
    
    Args:
        data: Input dataframe
        window_size: Size of the smoothing window
    
    Returns:
        Data with noisy sections smoothed
    """
    smoothed_data = data.copy()
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        valid_data = data[col].dropna()
        if len(valid_data) > window_size:
            # Calculate noise level (using rolling standard deviation)
            rolling_std = data[col].rolling(window=window_size, center=True).std()
            noise_threshold = rolling_std.quantile(0.8)  # 80th percentile
            
            # Identify noisy sections
            noisy_mask = rolling_std > noise_threshold
            
            if noisy_mask.any():
                # Apply smoothing only to noisy sections
                smoothed_values = uniform_filter1d(
                    data[col].fillna(method='ffill').fillna(method='bfill'),
                    size=window_size,
                    mode='nearest'
                )
                
                # Only replace values in noisy sections
                smoothed_data.loc[noisy_mask, col] = smoothed_values[noisy_mask]
                
                print(f"Smoothed {noisy_mask.sum()} noisy points in {col}")
    
    return smoothed_data


def adaptive_sequence_creation(data: pd.DataFrame, target_sequences: int = 10000) -> np.ndarray:
    """
    Create sequences with dynamic length adjustment based on data availability.
    
    Args:
        data: Input dataframe with well log data
        target_sequences: Target number of sequences to create
    
    Returns:
        Array of sequences with optimal length
    """
    sequence_lengths = [8, 16, 24, 32, 48, 64, 96, 128]
    
    print(f"Attempting adaptive sequence creation with target: {target_sequences} sequences")
    
    for seq_len in sequence_lengths:
        possible_sequences = calculate_possible_sequences(data, seq_len)
        print(f"Sequence length {seq_len}: {possible_sequences} possible sequences")
        
        if possible_sequences >= target_sequences:
            print(f"Selected sequence length: {seq_len}")
            return create_sequences(data, seq_len)
    
    # If no suitable length found, use shortest with maximum sequences
    print(f"Using shortest sequence length: {sequence_lengths[0]}")
    return create_sequences(data, sequence_lengths[0])


def calculate_possible_sequences(data: pd.DataFrame, seq_len: int, stride: int = 1) -> int:
    """
    Calculate the number of possible sequences for a given sequence length.
    
    Args:
        data: Input dataframe
        seq_len: Sequence length
        stride: Stride between sequences
    
    Returns:
        Number of possible sequences
    """
    if len(data) < seq_len:
        return 0
    
    # Count valid sequences (without NaN values)
    valid_sequences = 0
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    feature_cols = [col for col in numeric_cols if col.upper() != 'DEPTH']
    
    for start_idx in range(0, len(data) - seq_len + 1, stride):
        end_idx = start_idx + seq_len
        sequence_data = data[feature_cols].iloc[start_idx:end_idx]
        
        # Check if sequence has sufficient valid data
        if not sequence_data.isnull().any().any():
            valid_sequences += 1
    
    return valid_sequences


def create_sequences(data: pd.DataFrame, seq_len: int, stride: int = 1) -> np.ndarray:
    """
    Create sequences from dataframe data.
    
    Args:
        data: Input dataframe
        seq_len: Sequence length
        stride: Stride between sequences
    
    Returns:
        Array of sequences (n_sequences, seq_len, n_features)
    """
    # Select numeric columns excluding DEPTH if present
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    feature_cols = [col for col in numeric_cols if col.upper() != 'DEPTH']
    
    sequences = []
    
    for start_idx in range(0, len(data) - seq_len + 1, stride):
        end_idx = start_idx + seq_len
        sequence_data = data[feature_cols].iloc[start_idx:end_idx].values
        
        # Only add sequences without NaN values
        if not np.isnan(sequence_data).any():
            sequences.append(sequence_data)
    
    if sequences:
        return np.array(sequences)
    else:
        return np.array([]).reshape(0, seq_len, len(feature_cols))


def combine_well_sequences(wells_data: Dict[str, pd.DataFrame], min_sequences: int = 5000) -> np.ndarray:
    """
    Combine sequences from multiple wells with data augmentation if needed.
    
    Args:
        wells_data: Dictionary mapping well names to dataframes
        min_sequences: Minimum number of sequences required
    
    Returns:
        Combined array of sequences from all wells
    """
    combined_sequences = []
    
    print(f"Combining sequences from {len(wells_data)} wells...")
    
    for well_name, well_data in wells_data.items():
        print(f"Processing well: {well_name}")
        well_sequences = adaptive_sequence_creation(well_data, target_sequences=1000)
        
        if len(well_sequences) > 0:
            combined_sequences.append(well_sequences)
            print(f"Added {len(well_sequences)} sequences from {well_name}")
        else:
            print(f"No valid sequences from {well_name}")
    
    if combined_sequences:
        all_sequences = np.concatenate(combined_sequences, axis=0)
        print(f"Total sequences before augmentation: {len(all_sequences)}")
        
        if len(all_sequences) < min_sequences:
            print(f"Applying data augmentation to reach minimum: {min_sequences}")
            all_sequences = augment_sequences(all_sequences, target_count=min_sequences)
        
        print(f"Final sequence count: {len(all_sequences)}")
        return all_sequences
    else:
        print("No valid sequences found from any well")
        return np.array([])


def augment_sequences(sequences: np.ndarray, target_count: int) -> np.ndarray:
    """
    Augment sequences using noise injection and slight variations.
    
    Args:
        sequences: Input sequences array
        target_count: Target number of sequences after augmentation
    
    Returns:
        Augmented sequences array
    """
    if len(sequences) == 0:
        return sequences
    
    augmented_sequences = [sequences]
    current_count = len(sequences)
    
    while current_count < target_count:
        # Add noise to existing sequences
        noise_factor = 0.01  # 1% noise
        noisy_sequences = sequences + np.random.normal(0, noise_factor, sequences.shape)
        
        # Add time-shifted versions
        shifted_sequences = np.roll(sequences, shift=1, axis=1)
        
        # Combine augmentations
        new_sequences = np.concatenate([noisy_sequences, shifted_sequences], axis=0)
        
        # Limit to avoid infinite loop
        needed = target_count - current_count
        if len(new_sequences) > needed:
            new_sequences = new_sequences[:needed]
        
        augmented_sequences.append(new_sequences)
        current_count += len(new_sequences)
    
    final_sequences = np.concatenate(augmented_sequences, axis=0)
    
    # Trim to exact target count
    if len(final_sequences) > target_count:
        final_sequences = final_sequences[:target_count]
    
    print(f"Augmented from {len(sequences)} to {len(final_sequences)} sequences")
    return final_sequences


# Backward compatibility functions for existing workflow
def enhanced_normalize_data(df: pd.DataFrame, columns: List[str], scalers: Optional[Dict] = None) -> Tuple[pd.DataFrame, Dict]:
    """
    Drop-in replacement for existing normalize_data function with enhancements.

    Args:
        df: Input dataframe
        columns: Columns to normalize
        scalers: Optional pre-fitted scalers to use (for validation data)

    Returns:
        Tuple of (normalized_df, scalers_dict)
    """
    preprocessor = EnhancedLogPreprocessor()

    if scalers is not None:
        # Use existing scalers (for validation data)
        df_scaled = df.copy()
        for col in columns:
            if col in scalers and col in df.columns:
                valid_mask = ~df[col].isna()
                if valid_mask.any():
                    df_scaled.loc[valid_mask, col] = scalers[col].transform(
                        df[col][valid_mask].values.reshape(-1, 1)
                    ).flatten()
        return df_scaled, scalers
    else:
        # Fit new scalers (for training data)
        return preprocessor.normalize_data_enhanced(df, columns)


def enhanced_create_sequences(df: pd.DataFrame, well_col: str,
                            feature_cols: List[str], sequence_len: int = 64,
                            step: int = 1) -> np.ndarray:
    """
    Drop-in replacement for existing create_sequences function with enhancements.
    """
    preprocessor = EnhancedLogPreprocessor(
        sequence_len=sequence_len,
        sequence_stride=step
    )
    sequences, _ = preprocessor.create_sequences_enhanced(df, well_col, feature_cols)
    return sequences


def enhanced_introduce_missingness(sequences: np.ndarray, missing_rate: float = 0.2,
                                 random_seed: int = 42) -> np.ndarray:
    """
    Drop-in replacement for existing introduce_missingness function with enhancements.
    """
    preprocessor = EnhancedLogPreprocessor(missing_rate=missing_rate, random_seed=random_seed)
    return preprocessor.introduce_realistic_missingness(sequences)
